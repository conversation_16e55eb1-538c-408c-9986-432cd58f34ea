# yaml-language-server: $schema=https://raw.githubusercontent.com/antinomyhq/forge/refs/heads/main/forge.schema.json
agents:
- id: forge
  tools:
  - forge_tool_task_list_append
  - forge_tool_task_list_append_multiple
  - forge_tool_task_list_mark_done
  - forge_tool_task_list_list
  - forge_tool_task_list_clear
variables:
  operating_agent: forge
commands:
- name: fixme
  description: Looks for all the fixme comments in the code and attempts to fix them
  prompt: Find all the FIXME comments in source-code files and attempt to fix them.
- name: pr-description
  description: Updates the description of the PR
  prompt: |-
    - I have created a Pull Request with all the accepted changes
    - Understand the current PR deeply using the GH CLI and update the PR title and description
    - Make sure the title follows conventional commits standard
    - Top-level summary should contain 2-3 lines about the core functionality improvements
- name: check
  description: Checks if the code is ready to be committed
  prompt: |-
    - Run the `lint` and `test` commands and verify if everything is fine.
      <lint>cargo +nightly fmt --all; cargo +nightly clippy --fix --allow-staged --allow-dirty --workspace</lint>
      <test>cargo insta test --accept --unreferenced=delete</test>
    - Fix every issue found in the process
model: anthropic/claude-sonnet-4
max_walker_depth: 1024
custom_rules: |-
  Handling Errors:

  - Use `anyhow::Result` for error handling in services and repositories.
  - Create domain errors using `thiserror`.
  - Never implement `From` for converting domain errors, manually convert them

  Writing Tests:

  - All tests should be written in three discrete steps:

    ```rust
    use pretty_assertions::assert_eq; // Always use pretty assertions

    fn test_foo() {
        let fixture = ...; // Instantiate a fixture for the test
        let actual = ...; // Execute the fixture to create an output
        let expected = ...; // Define a hand written expected result
        assert_eq!(actual, expected); // Assert that the actual result matches the expected result
    }
    ```

  - Use `pretty_assertions` for better error messages.
  - Use fixtures to create test data.
  - Use `assert_eq!` for equality checks.
  - Use `assert!(...)` for boolean checks.
  - Use unwraps in test functions and anyhow::Result in fixtures.
  - Keep the boilerplate to a minimum.
  - Use words like `fixture`, `actual` and `expected` in test functions.
  - Fixtures should be generic and reusable.
  - Test should always be written in the same file as the source code.

  Running Tests:
  - We use `insta` to run tests:
  ```
  cargo insta test --accept --unreferenced=delete
  ```

  Verification:
  - run the following command to format and validate if the code is working:
    ```
    cargo +nightly fmt --all; cargo +nightly clippy --fix --allow-staged --allow-dirty --workspace;
    ```

  Writing Domain Types:
  - Use `derive_setters` to derive setters and use the `strip_option` and the `into` attributes on the struct types.


  Refactoring:
  - If asked to fix failing tests, always confirm whether to update the implementation or the tests.
  Elm Architecture:
  - Command executors should ALWAYS return Option<Action>, never send them directly through channels
  - Actions are the only way to update application state
  - State updates trigger UI changes through the unidirectional data flow
  - Commands represent intent to perform side effects
  - Actions represent the result of those side effects
  - The executor pattern: Command -> Side Effect -> Action -> State Update -> UI Update
