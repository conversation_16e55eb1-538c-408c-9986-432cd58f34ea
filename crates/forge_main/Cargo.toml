[package]
name = "forge_main"
version = "0.1.0"
edition = "2024"

[[bin]]
name = "forge"
path = "src/main.rs"


[dependencies]
clap.workspace = true
console.workspace = true
forge_api.workspace = true
forge_domain.workspace = true
forge_walker.workspace = true
forge_display.workspace = true
forge_tracker.workspace = true
forge_snaps.workspace = true
forge_spinner.workspace = true
inquire.workspace = true
serde_yml.workspace = true
merge.workspace = true

forge_fs.workspace = true
forge_main_neo.workspace = true
tokio.workspace = true
tokio-stream.workspace = true
colored.workspace = true
async-trait.workspace = true
anyhow.workspace = true
derive_setters.workspace = true
lazy_static.workspace = true
reedline.workspace = true
nu-ansi-term.workspace = true
nucleo.workspace = true
tracing.workspace = true
tracing-appender.workspace = true
tracing-subscriber.workspace = true
chrono.workspace = true
serde_json.workspace = true
serde.workspace = true
strum.workspace = true
strum_macros.workspace = true
base64.workspace = true
convert_case.workspace = true
update-informer = { version = "1.2.0", default-features = false, features = [
    "npm",
    "ureq",
    "rustls-tls",
] }
open.workspace = true

[dev-dependencies]
insta.workspace = true
pretty_assertions.workspace = true
tempfile.workspace = true
