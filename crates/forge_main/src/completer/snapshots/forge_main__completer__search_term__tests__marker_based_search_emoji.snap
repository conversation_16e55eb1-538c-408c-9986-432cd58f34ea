---
source: crates/forge_main/src/completer/search_term.rs
expression: results
---
[
    TermSpec {
        input: "@[🚀 @🌟 emoji@",
        output: Some(
            "",
        ),
        span_start: Some(
            1,
        ),
        span_end: Some(
            1,
        ),
        pos: 1,
    },
    TermSpec {
        input: "@🚀[ @🌟 emoji@",
        output: Some(
            "🚀",
        ),
        span_start: Some(
            1,
        ),
        span_end: Some(
            5,
        ),
        pos: 5,
    },
    TermSpec {
        input: "@🚀 [@🌟 emoji@",
        output: None,
        span_start: None,
        span_end: None,
        pos: 6,
    },
    TermSpec {
        input: "@🚀 @[🌟 emoji@",
        output: Some(
            "",
        ),
        span_start: Some(
            7,
        ),
        span_end: Some(
            7,
        ),
        pos: 7,
    },
    TermSpec {
        input: "@🚀 @🌟[ emoji@",
        output: Some(
            "🌟",
        ),
        span_start: Some(
            7,
        ),
        span_end: Some(
            11,
        ),
        pos: 11,
    },
    TermSpec {
        input: "@🚀 @🌟 [emoji@",
        output: None,
        span_start: None,
        span_end: None,
        pos: 12,
    },
    TermSpec {
        input: "@🚀 @🌟 e[moji@",
        output: None,
        span_start: None,
        span_end: None,
        pos: 13,
    },
    TermSpec {
        input: "@🚀 @🌟 em[oji@",
        output: None,
        span_start: None,
        span_end: None,
        pos: 14,
    },
    TermSpec {
        input: "@🚀 @🌟 emo[ji@",
        output: None,
        span_start: None,
        span_end: None,
        pos: 15,
    },
    TermSpec {
        input: "@🚀 @🌟 emoj[i@",
        output: None,
        span_start: None,
        span_end: None,
        pos: 16,
    },
    TermSpec {
        input: "@🚀 @🌟 emoji[@",
        output: None,
        span_start: None,
        span_end: None,
        pos: 17,
    },
    TermSpec {
        input: "@🚀 @🌟 emoji@[",
        output: Some(
            "",
        ),
        span_start: Some(
            18,
        ),
        span_end: Some(
            18,
        ),
        pos: 18,
    },
]
