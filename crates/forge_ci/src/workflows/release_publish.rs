use gh_workflow_tailcall::generate::Generate;
use gh_workflow_tailcall::*;

use crate::jobs::{ReleaseBuilderJob, release_homebrew_job, release_npm_job};

/// Generate npm release workflow
pub fn release_publish() {
    let release_build_job = ReleaseBuilderJob::new("${{ github.event.release.tag_name }}")
        .release_id("${{ github.event.release.id }}");
    let npm_release_job = release_npm_job().add_needs(release_build_job.clone());
    let homebrew_release_job = release_homebrew_job().add_needs(release_build_job.clone());

    let npm_workflow = Workflow::default()
        .name("Multi Channel Release")
        .on(Event {
            release: Some(Release { types: vec![ReleaseType::Published] }),
            ..Event::default()
        })
        .permissions(
            Permissions::default()
                .contents(Level::Write)
                .pull_requests(Level::Write),
        )
        .add_job("build_release", release_build_job)
        .add_job("npm_release", npm_release_job)
        .add_job("homebrew_release", homebrew_release_job);

    Generate::new(npm_workflow)
        .name("release.yml")
        .generate()
        .unwrap();
}
