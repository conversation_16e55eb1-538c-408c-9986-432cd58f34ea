[package]
name = "forge_tracker"
version = "0.1.0"
edition = "2024"

[dependencies]
reqwest.workspace = true
derive_more.workspace = true
url.workspace = true
serde.workspace = true
serde_json.workspace = true
machineid-rs.workspace = true
tokio.workspace = true
tracing.workspace = true
sysinfo.workspace = true
posthog-rs = { git = "https://github.com/PostHog/posthog-rs.git", rev = "a006a81419031e4889d9c3882d7458d2efa588a8" }
async-trait.workspace = true
chrono.workspace = true
whoami.workspace = true
convert_case.workspace = true
http.workspace = true
regex.workspace = true
tracing-appender.workspace = true
tracing-subscriber.workspace = true
anyhow.workspace = true
forge_domain.workspace = true

[dev-dependencies]
lazy_static.workspace = true
strum.workspace = true