[package]
name = "forge_provider"
version = "0.1.0"
edition = "2024"

[dependencies]
regex.workspace = true
tokio.workspace = true
tokio-stream.workspace = true
reqwest.workspace = true
serde.workspace = true
serde_json.workspace = true
tracing.workspace = true
async-trait.workspace = true
derive_more.workspace = true
derive_setters.workspace = true
reqwest-eventsource.workspace = true
strum.workspace = true
strum_macros.workspace = true
forge_app.workspace = true
anyhow.workspace = true
thiserror.workspace = true
derive_builder.workspace = true

[dev-dependencies]
insta.workspace = true
pretty_assertions.workspace = true
mockito.workspace = true
