{"error": {"code": "ERR_STREAM_PREMATURE_CLOSE"}}
{"error": {"errno": -104, "code": "ECONNRESET", "syscall": "read"}}
{"error": {"errno": -110, "code": "ETIMEDOUT", "syscall": "read"}}
{"error": {"error": {"code": "ERR_STREAM_PREMATURE_CLOSE"}}}
{"error": {"error": {"message": "Provider returned error", "code": 400, "metadata": {"raw": "{\"type\":\"error\",\"error\":{\"type\":\"invalid_request_error\",\"message\":\"input length and `max_tokens` exceed context limit: MIN + MAX > 200000, decrease input length or `max_tokens` and try again\"}}", "provider_name": "Google"}}, "code": 400}}
{"error": {"error": {"message": "Provider returned error", "code": 413, "metadata": {"raw": "{\"type\":\"error\",\"error\":{\"type\":\"invalid_request_error\",\"message\":\"Prompt is too long\"}}", "provider_name": "Google"}}, "code": 413}}
{"error": {"error": {"message": "Provider returned error", "code": 503, "metadata": {"raw": "{\n  \"error\": {\n    \"code\": 503,\n    \"message\": \"The service is currently unavailable.\",\n    \"status\": \"UNAVAILABLE\"\n  }\n}\n", "provider_name": "Google AI Studio"}}, "code": 503}}
{"error": {"error": {"message": "Rate limit exceeded: limit_rpm/google/gemini-2.5-pro-exp-03-25/e5b7a905-dd02-4f6e-b61b-3aa1a1b8b9c8. ", "code": 429, "metadata": {"headers": {"X-RateLimit-Limit": "1", "X-RateLimit-Remaining": "0", "X-RateLimit-Reset": "1746315180000"}, "provider_name": null}}, "code": 429}}
{"error": {"error": {"message": "Rate limit exceeded: limit_rpm/google/gemini-2.5-pro-exp-03-25/e5b7a905-dd02-4f6e-b61b-3aa1a1b8b9c8. High demand for google/gemini-2.5-pro-exp-03-25 on OpenRouter - limited to 1 requests per minute. Please retry shortly.", "code": 429, "metadata": {"headers": {"X-RateLimit-Limit": "1", "X-RateLimit-Remaining": "0", "X-RateLimit-Reset": "1746937440000"}, "provider_name": null}}, "code": 429}}
{"error": {"error": {"message": "This endpoint's maximum context length is 200000 tokens. However, you requested about X tokens (Y of text input). Please reduce the length of either one, or use the \"middle-out\" transform to compress your prompt automatically.", "code": 400, "metadata": {"provider_name": null}}, "code": 400}}
{"error": {"error": {"message": "Upstream error from Chutes: Requested token count exceeds the model's maximum context length of 32768 tokens. You requested a total of 34689 tokens: 22892 tokens from the input messages and 11797 tokens for the completion. Please reduce the number of tokens in the input messages or the completion to fit within the limit.", "code": 502, "metadata": {"raw": {"object": "error", "message": "Requested token count exceeds the model's maximum context length of 32768 tokens. You requested a total of 34689 tokens: 22892 tokens from the input messages and 11797 tokens for the completion. Please reduce the number of tokens in the input messages or the completion to fit within the limit.", "type": "BadRequestError", "param": null, "code": 400}, "provider_name": "Chutes"}}, "code": 502}}
{"error": {"message": "400: An error occurred (ValidationException) when calling the ConverseStream operation: The text field in the ContentBlock object at messages.19.content.0 is blank. Add text to the text field, and try again."}}
{"error": {"message": "500: An error occurred (AccessDeniedException) when calling the ConverseStream operation: User: arn:aws:sts::275481790157:assumed-role/bedrockpartnerawsv1-ProxyApiHandlerServiceRoleBE71B-LZI8y019SUOG/bedrockpartnerawsv1-ProxyApiHandlerEC15A492-1JsCZPLohxs3 is not authorized to perform: bedrock:InvokeModelWithResponseStream on resource: arn:aws:bedrock:us-west-2::foundation-model/anthropic.claude-3-7-sonnet-20250219-v1:0 with an explicit deny in a service control policy"}}
{"error": {"message": "An error occurred (modelStreamErrorException) when calling the ConverseStream operation: The system encountered an unexpected error during processing. Try your request again."}}
{"error": {"message": "An error occurred (serviceUnavailableException) when calling the ConverseStream operation: Bedrock is unable to process your request."}}
{"error": {"message": "An error occurred (validationException) when calling the ConverseStream operation: The model returned the following errors: Input is too long for requested model."}}
{"error": {"message": "litellm.APIConnectionError: AnthropicException - ", "type": null, "param": null, "code": "500"}}
{"error": {"message": "litellm.BadRequestError: BedrockException - validationException {\"message\":\"The model returned the following errors: input length and `max_tokens` exceed context limit: 197717 + 8192 > 204698, decrease input length or `max_tokens` and try again\"}", "type": null, "param": null, "code": "400"}}
{"error": {"message": "litellm.ContextWindowExceededError: litellm.BadRequestError: BedrockException: Context Window Error - validationException {\"message\":\"The model returned the following errors: Input is too long for requested model.\"}", "type": null, "param": null, "code": "400"}}
{"error": {"message": "litellm.InternalServerError: AnthropicException - Overloaded. Handle with `litellm.InternalServerError`.", "type": null, "param": null, "code": "500"}}