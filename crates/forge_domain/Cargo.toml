[package]
name = "forge_domain"
version = "0.1.0"
edition = "2024"

[dependencies]
anyhow.workspace = true
async-trait.workspace = true
chrono.workspace = true
derive_more.workspace = true
derive_setters.workspace = true
nom.workspace = true
schemars.workspace = true
serde.workspace = true
eserde = {version= "0.1.7", features=["json"]}
serde_json.workspace = true
strum.workspace = true
strum_macros.workspace = true
thiserror.workspace = true
tokio.workspace = true
tokio-stream.workspace = true
uuid.workspace = true
tracing.workspace = true
url.workspace = true
merge.workspace = true
serde_yml.workspace = true
forge_template.workspace = true
base64.workspace = true
derive-getters = "0.5.0"
regex.workspace = true
forge_tool_macros.workspace = true
lazy_static = "1.5.0"
convert_case.workspace = true

[dev-dependencies]
insta = { workspace = true, features = ["yaml"] }
pretty_assertions.workspace = true
is_ci.workspace = true
