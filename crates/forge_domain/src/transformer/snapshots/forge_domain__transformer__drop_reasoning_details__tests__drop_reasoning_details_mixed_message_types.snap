---
source: crates/forge_domain/src/transformer/drop_reasoning_details.rs
expression: snapshot
---
transformation: DropReasoningDetails_mixed_messages
before:
  messages:
    - text:
        role: System
        content: System message
        tool_calls: ~
        model: ~
        reasoning_details: ~
    - text:
        role: User
        content: User message with reasoning
        tool_calls: ~
        model: ~
        reasoning_details:
          - text: Complex reasoning process
            signature: ~
    - text:
        role: User
        content: User message without reasoning
        tool_calls: ~
        model: ~
        reasoning_details: ~
    - text:
        role: Assistant
        content: Assistant response
        tool_calls: ~
        model: ~
        reasoning_details: ~
    - tool:
        name: test_tool
        call_id: call_123
        output:
          is_error: false
          values:
            - text: Tool result
after:
  messages:
    - text:
        role: System
        content: System message
        tool_calls: ~
        model: ~
        reasoning_details: ~
    - text:
        role: User
        content: User message with reasoning
        tool_calls: ~
        model: ~
        reasoning_details: ~
    - text:
        role: User
        content: User message without reasoning
        tool_calls: ~
        model: ~
        reasoning_details: ~
    - text:
        role: Assistant
        content: Assistant response
        tool_calls: ~
        model: ~
        reasoning_details: ~
    - tool:
        name: test_tool
        call_id: call_123
        output:
          is_error: false
          values:
            - text: Tool result
