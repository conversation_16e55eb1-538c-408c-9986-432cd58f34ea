---
source: crates/forge_domain/src/transformer/set_model.rs
expression: snapshot
---
transformation: SetModel(gpt-4)
before:
  messages:
    - text:
        role: System
        content: System message
        tool_calls: ~
        model: ~
        reasoning_details: ~
    - text:
        role: User
        content: User message 1
        tool_calls: ~
        model: ~
        reasoning_details: ~
    - text:
        role: Assistant
        content: Assistant response
        tool_calls: ~
        model: ~
        reasoning_details: ~
    - text:
        role: User
        content: User message 2
        tool_calls: ~
        model: ~
        reasoning_details: ~
after:
  messages:
    - text:
        role: System
        content: System message
        tool_calls: ~
        model: ~
        reasoning_details: ~
    - text:
        role: User
        content: User message 1
        tool_calls: ~
        model: gpt-4
        reasoning_details: ~
    - text:
        role: Assistant
        content: Assistant response
        tool_calls: ~
        model: ~
        reasoning_details: ~
    - text:
        role: User
        content: User message 2
        tool_calls: ~
        model: gpt-4
        reasoning_details: ~
