---
source: crates/forge_domain/src/transformer/transform_tool_calls.rs
expression: snapshot
---
transformation: "TransformToolCalls::with_model(gpt-4)"
before:
  messages:
    - text:
        role: System
        content: System message
        tool_calls: ~
        model: ~
        reasoning_details: ~
    - text:
        role: Assistant
        content: "I'll help you"
        tool_calls:
          - name: test_tool
            call_id: call_123
            arguments:
              param: value
        model: ~
        reasoning_details: ~
    - tool:
        name: test_tool
        call_id: call_123
        output:
          is_error: false
          values:
            - text: Tool result text
after:
  messages:
    - text:
        role: System
        content: System message
        tool_calls: ~
        model: ~
        reasoning_details: ~
    - text:
        role: Assistant
        content: "I'll help you"
        tool_calls: ~
        model: ~
        reasoning_details: ~
    - text:
        role: User
        content: Tool result text
        tool_calls: ~
        model: gpt-4
        reasoning_details: ~
