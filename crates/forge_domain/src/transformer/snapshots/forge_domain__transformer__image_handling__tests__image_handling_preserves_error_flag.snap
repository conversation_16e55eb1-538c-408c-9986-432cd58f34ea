---
source: crates/forge_domain/src/transformer/image_handling.rs
expression: snapshot
---
transformation: ImageHandling
before:
  messages:
    - tool:
        name: error_tool
        call_id: call_error
        output:
          is_error: true
          values:
            - text: Error occurred
            - image:
                url: "data:image/png;base64,error_image_data"
                mime_type: image/png
after:
  messages:
    - tool:
        name: error_tool
        call_id: call_error
        output:
          is_error: true
          values:
            - text: Error occurred
            - text: "[The image with ID 0 will be sent as an attachment in the next message]"
    - text:
        role: User
        content: "[Here is the image attachment for ID 0]"
        tool_calls: ~
        model: ~
        reasoning_details: ~
    - image:
        url: "data:image/png;base64,error_image_data"
        mime_type: image/png
