---
source: crates/forge_domain/src/context.rs
expression: actual
---
messages:
  - text:
      role: System
      content: System message
      tool_calls: ~
      model: ~
      reasoning_details: ~
  - tool:
      name: image_tool
      call_id: call1
      output:
        is_error: false
        values:
          - text: "[The image with ID 0 will be sent as an attachment in the next message]"
  - text:
      role: User
      content: "[Here is the image attachment for ID 0]"
      tool_calls: ~
      model: ~
      reasoning_details: ~
  - image:
      url: "data:image/png;base64,test123"
      mime_type: image/png
