---
source: crates/forge_domain/src/tool_usage.rs
expression: prompt
---
<tool>{"name":"forge_tool_fs_read","description":"Reads file contents from the specified absolute path. Ideal for analyzing\n code, configuration files, documentation, or textual data. Automatically\n extracts text from PDF and DOCX files, preserving the original formatting.\n Returns the content as a string. For files larger than 2,000 lines,\n the tool automatically returns only the first 2,000 lines. You should\n always rely on this default behavior and avoid specifying custom ranges\n unless absolutely necessary. If needed, specify a range with the start_line\n and end_line parameters, ensuring the total range does not exceed 2,000\n lines. Specifying a range exceeding this limit will result in an error.\n Binary files are automatically detected and rejected.","arguments":{"end_line":{"description":"Optional end position in lines (inclusive). If provided, reading will end at this line position.","type":"integer","is_required":false},"explanation":{"description":"One sentence explanation as to why this specific tool is being used, and how it contributes to the goal.","type":"string","is_required":false},"path":{"description":"The path of the file to read, always provide absolute paths.","type":"string","is_required":true},"start_line":{"description":"Optional start position in lines (1-based). If provided, reading will start from this line position.","type":"integer","is_required":false}}}</tool>
<tool>{"name":"forge_tool_fs_create","description":"Use it to create a new file at a specified path with the provided content.\n Always provide absolute paths for file locations. The tool\n automatically handles the creation of any missing intermediary directories\n in the specified path.\n IMPORTANT: DO NOT attempt to use this tool to move or rename files, use the\n shell tool instead.","arguments":{"content":{"description":"The content to write to the file. ALWAYS provide the COMPLETE intended content of the file, without any truncation or omissions. You MUST include ALL parts of the file, even if they haven't been modified.","type":"string","is_required":true},"explanation":{"description":"One sentence explanation as to why this specific tool is being used, and how it contributes to the goal.","type":"string","is_required":false},"overwrite":{"description":"If set to true, existing files will be overwritten. If not set and the file exists, an error will be returned with the content of the existing file.","type":"boolean","is_required":false},"path":{"description":"The path of the file to write to (absolute path required)","type":"string","is_required":true}}}</tool>
<tool>{"name":"forge_tool_fs_search","description":"Recursively searches directories for files by content (regex) and/or name\n (glob pattern). Provides context-rich results with line numbers for content\n matches. Two modes: content search (when regex provided) or file finder\n (when regex omitted). Uses case-insensitive Rust regex syntax. Requires\n absolute paths. Avoids binary files and excluded directories. Best for code\n exploration, API usage discovery, configuration settings, or finding\n patterns across projects. For large pages, returns the first 200\n lines and stores the complete content in a temporary file for\n subsequent access.","arguments":{"explanation":{"description":"One sentence explanation as to why this specific tool is being used, and how it contributes to the goal.","type":"string","is_required":false},"file_pattern":{"description":"Glob pattern to filter files (e.g., '*.ts' for TypeScript files). If not provided, it will search all files (*).","type":"string","is_required":false},"max_search_lines":{"description":"Maximum number of lines to return in the search results.","type":"integer","is_required":false},"path":{"description":"The absolute path of the directory or file to search in. If it's a directory, it will be searched recursively. If it's a file path, only that specific file will be searched.","type":"string","is_required":true},"regex":{"description":"The regular expression pattern to search for in file contents. Uses Rust regex syntax. If not provided, only file name matching will be performed.","type":"string","is_required":false},"start_index":{"description":"Starting index for the search results (1-based).","type":"integer","is_required":false}}}</tool>
<tool>{"name":"forge_tool_fs_remove","description":"Request to remove a file at the specified path. Use this when you need to\n delete an existing file. The path must be absolute. This operation cannot\n be undone, so use it carefully.","arguments":{"explanation":{"description":"One sentence explanation as to why this specific tool is being used, and how it contributes to the goal.","type":"string","is_required":false},"path":{"description":"The path of the file to remove (absolute path required)","type":"string","is_required":true}}}</tool>
<tool>{"name":"forge_tool_fs_patch","description":"Modifies files with targeted line operations on matched patterns. Supports\n prepend, append, replace, replace_all, swap, delete\n operations. Ideal for precise changes to configs, code, or docs while\n preserving context. Not suitable for complex refactoring or modifying all\n pattern occurrences - use `forge_tool_fs_create` instead for complete\n rewrites and `forge_tool_fs_undo` for undoing the last operation. Fails if\n search pattern isn\\'t found.","arguments":{"content":{"description":"The content to use for the operation (replacement text, line to prepend/append, or target line for swap operations)","type":"string","is_required":true},"explanation":{"description":"One sentence explanation as to why this specific tool is being used, and how it contributes to the goal.","type":"string","is_required":false},"operation":{"description":"The operation to perform on the matched text. Possible options are: - 'prepend': Add content before the matched text - 'append': Add content after the matched text - 'replace': Use only for specific, targeted replacements where you need to modify just the first match. - 'replace_all': Should be used for renaming variables, functions, types, or any widespread replacements across the file. This is the recommended choice for consistent refactoring operations as it ensures all occurrences are updated. - 'swap': Replace the matched text with another text (search for the second text and swap them)","type":"string","is_required":true},"path":{"description":"The path to the file to modify","type":"string","is_required":true},"search":{"description":"The exact line to search for in the file. When skipped the patch operation applies to the entire content. `Append` adds the new content to the end, `Prepend` adds it to the beginning, and `Replace` fully overwrites the original content. `Swap` requires a search target, so without one, it makes no changes.","type":"string","is_required":false}}}</tool>
<tool>{"name":"forge_tool_fs_undo","description":"Reverts the most recent file operation (create/modify/delete) on a specific\n file. Use this tool when you need to recover from incorrect file changes or\n if a revert is requested by the user.","arguments":{"explanation":{"description":"One sentence explanation as to why this specific tool is being used, and how it contributes to the goal.","type":"string","is_required":false},"path":{"description":"The absolute path of the file to revert to its previous state.","type":"string","is_required":true}}}</tool>
<tool>{"name":"forge_tool_process_shell","description":"Executes shell commands with safety measures using restricted bash (rbash).\n Prevents potentially harmful operations like absolute path execution and\n directory changes. Use for file system interaction, running utilities,\n installing packages, or executing build commands. For operations requiring\n unrestricted access, advise users to run forge CLI with \\'-u\\' flag. Returns\n complete output including stdout, stderr, and exit code for diagnostic\n purposes.","arguments":{"command":{"description":"The shell command to execute.","type":"string","is_required":true},"cwd":{"description":"The working directory where the command should be executed.","type":"string","is_required":true},"explanation":{"description":"One sentence explanation as to why this specific tool is being used, and how it contributes to the goal.","type":"string","is_required":false},"keep_ansi":{"description":"Whether to preserve ANSI escape codes in the output. If true, ANSI escape codes will be preserved in the output. If false (default), ANSI escape codes will be stripped from the output.","type":"boolean","is_required":false}}}</tool>
<tool>{"name":"forge_tool_net_fetch","description":"Input type for the net fetch tool","arguments":{"explanation":{"description":"One sentence explanation as to why this specific tool is being used, and how it contributes to the goal.","type":"string","is_required":false},"raw":{"description":"Get raw content without any markdown conversion (default: false)","type":"boolean","is_required":false},"url":{"description":"URL to fetch","type":"string","is_required":true}}}</tool>
<tool>{"name":"forge_tool_followup","description":"Use this tool when you encounter ambiguities, need clarification, or require\n more details to proceed effectively. Use this tool judiciously to maintain a\n balance between gathering necessary information and avoiding excessive\n back-and-forth.","arguments":{"explanation":{"description":"One sentence explanation as to why this specific tool is being used, and how it contributes to the goal.","type":"string","is_required":false},"multiple":{"description":"If true, allows selecting multiple options; if false (default), only one option can be selected","type":"boolean","is_required":false},"option1":{"description":"First option to choose from","type":"string","is_required":false},"option2":{"description":"Second option to choose from","type":"string","is_required":false},"option3":{"description":"Third option to choose from","type":"string","is_required":false},"option4":{"description":"Fourth option to choose from","type":"string","is_required":false},"option5":{"description":"Fifth option to choose from","type":"string","is_required":false},"question":{"description":"Question to ask the user","type":"string","is_required":true}}}</tool>
<tool>{"name":"forge_tool_attempt_completion","description":"After each tool use, the user will respond with the result of\n that tool use, i.e. if it succeeded or failed, along with any reasons for\n failure. Once you\\'ve received the results of tool uses and can confirm that\n the task is complete, use this tool to present the result of your work to\n the user. The user may respond with feedback if they are not satisfied with\n the result, which you can use to make improvements and try again.\n IMPORTANT NOTE: This tool CANNOT be used until you\\'ve confirmed from the\n user that any previous tool uses were successful. Failure to do so will\n result in code corruption and system failure. Before using this tool, you\n must ask yourself in <forge_thinking></forge_thinking> tags if you\\'ve\n confirmed from the user that any previous tool uses were successful. If not,\n then DO NOT use this tool.","arguments":{"result":{"description":"The result of the task. Formulate this result in a way that is final and does not require further input from the user. Don't end your result with questions or offers for further assistance.","type":"string","is_required":true}}}</tool>
<tool>{"name":"forge_tool_task_list_append","description":"Add a new task to the end of the task list. Tasks are stored in conversation\n state and persist across agent interactions. Use this tool to add individual\n work items that need to be tracked during development sessions. Task IDs are\n auto-generated integers starting from 1.","arguments":{"explanation":{"description":"One sentence explanation as to why this specific tool is being used, and how it contributes to the goal.","type":"string","is_required":false},"task":{"description":"The task description to add to the list","type":"string","is_required":true}}}</tool>
<tool>{"name":"forge_tool_task_list_append_multiple","description":"Add multiple new tasks to the end of the task list. Tasks are stored in\n conversation state and persist across agent interactions. Use this tool to\n add several work items at once during development sessions. Task IDs are\n auto-generated integers starting from 1.","arguments":{"explanation":{"description":"One sentence explanation as to why this specific tool is being used, and how it contributes to the goal.","type":"string","is_required":false},"tasks":{"description":"The list of task descriptions to add","type":"array","is_required":true}}}</tool>
<tool>{"name":"forge_tool_task_list_update","description":"Update the status of a specific task in the task list. Use this when a\n task\\'s status changes (e.g., from Pending to InProgress, InProgress to Done,\n etc.). The task will remain in the list but with an updated status.","arguments":{"explanation":{"description":"One sentence explanation as to why this specific tool is being used, and how it contributes to the goal.","type":"string","is_required":false},"status":{"description":"The new status for the task","type":"string","is_required":true},"task_id":{"description":"The ID of the task to update","type":"integer","is_required":true}}}</tool>
<tool>{"name":"forge_tool_task_list_list","description":"Display the current task list with statistics. Shows all tasks with their\n IDs, descriptions, and status (PENDING, IN_PROGRESS, DONE), along with\n summary statistics. Use this tool to review current work items and track\n progress through development sessions.","arguments":{"explanation":{"description":"One sentence explanation as to why this specific tool is being used, and how it contributes to the goal.","type":"string","is_required":false}}}</tool>
<tool>{"name":"forge_tool_task_list_clear","description":"Remove all tasks from the task list. This operation cannot be undone and\n will reset the task ID counter to 1. Use this tool when you want to start\n fresh with a clean task list.","arguments":{"explanation":{"description":"One sentence explanation as to why this specific tool is being used, and how it contributes to the goal.","type":"string","is_required":false}}}</tool>
