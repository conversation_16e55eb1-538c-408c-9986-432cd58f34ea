[package]
name = "forge_api"
version = "0.1.0"
edition = "2024"

[dependencies]
anyhow.workspace = true
async-trait.workspace = true
forge_domain.workspace = true
forge_stream.workspace = true
forge_services.workspace = true
forge_walker.workspace = true
forge_infra.workspace = true
forge_snaps.workspace = true
serde_yml.workspace = true
serde_json.workspace = true
merge.workspace = true
bytes.workspace = true
tracing.workspace = true
tokio.workspace = true
futures.workspace = true
forge_app.workspace = true

[dev-dependencies]
tempfile.workspace = true
tokio = { workspace = true }
insta.workspace = true
serde.workspace = true
