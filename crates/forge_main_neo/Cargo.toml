[package]
name = "forge_main_neo"
version = "0.1.0"
edition = "2024"

[dependencies]
ratatui = "0.29.0"
tui-scrollview = "0.5.1"
forge_api.workspace = true
color-eyre = "0.6.5"
edtui = { version = "0.9.6", features = ["mouse-support"] }
derive_more.workspace = true
anyhow.workspace = true
tokio.workspace = true
tokio-stream.workspace = true
tokio-util.workspace = true
forge_display.workspace = true
chrono.workspace = true
derive_setters.workspace = true
strum.workspace = true
strum_macros.workspace = true
merge.workspace = true
serde_json.workspace = true
async-recursion.workspace = true
throbber-widgets-tui = "0.8.0"
forge_tracker.workspace = true
lazy_static.workspace = true
tracing.workspace = true
futures.workspace = true
ansi-to-tui.workspace = true

[dev-dependencies]
pretty_assertions.workspace = true
