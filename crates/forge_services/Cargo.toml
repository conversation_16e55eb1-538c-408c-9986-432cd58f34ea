[package]
name = "forge_services"
version = "0.1.0"
edition = "2024"

[dependencies]
uuid.workspace = true
chrono.workspace = true
tokio.workspace = true
derive_more.workspace = true
tracing.workspace = true
async-trait.workspace = true
async-recursion.workspace = true
forge_provider.workspace = true
forge_tool_macros.workspace = true
forge_display.workspace = true
forge_snaps.workspace = true
serde.workspace = true
serde_json.workspace = true
derive_setters.workspace = true
tokio-stream.workspace = true
handlebars.workspace = true
forge_fs.workspace = true
moka2.workspace = true
schemars.workspace = true
anyhow.workspace = true
futures.workspace = true
reqwest.workspace = true
regex.workspace = true
dissimilar.workspace = true
syn.workspace = true
thiserror.workspace = true
nom.workspace = true
tree-sitter.workspace = true
html2md.workspace = true
glob.workspace = true
grep-searcher.workspace = true
grep-regex.workspace = true
tree-sitter-rust.workspace = true
tree-sitter-python.workspace = true
tree-sitter-typescript.workspace = true
tree-sitter-css.workspace = true
tree-sitter-java.workspace = true
tree-sitter-scala.workspace = true
tree-sitter-go.workspace = true
tree-sitter-cpp.workspace = true
tree-sitter-ruby.workspace = true
rust-embed.workspace = true
base64.workspace = true
strum_macros.workspace = true
strum.workspace = true
bytes.workspace = true
console.workspace = true
serde_yml.workspace = true
merge.workspace = true
strip-ansi-escapes.workspace = true
rmcp.workspace = true
forge_app.workspace = true
url.workspace = true

[dev-dependencies]
insta.workspace = true
mockito.workspace = true
pretty_assertions.workspace = true
tempfile.workspace = true
