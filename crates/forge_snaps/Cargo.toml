[package]
name = "forge_snaps"
version = "0.1.0"
edition = "2024"

[dependencies]
anyhow.workspace = true
forge_fs.workspace = true
forge_walker.workspace = true
fnv_rs.workspace = true
base64.workspace = true
serde_json.workspace = true
serde.workspace = true
futures.workspace = true
uuid = { workspace = true, features = ["v4", "serde"] }
chrono.workspace = true
tokio = { workspace = true, features = ["full"] }

[dev-dependencies]
tempfile.workspace = true