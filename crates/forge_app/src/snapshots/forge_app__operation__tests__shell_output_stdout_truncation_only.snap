---
source: crates/forge_app/src/operation.rs
expression: to_value(actual)
---
<shell_output
  command="long_command"
  shell="/bin/bash"
  exit_code="0"
>
<stdout
  total_lines="25"
  full_output="/tmp/stdout_content.txt"
>
<head
  display_lines="1-10"
><![CDATA[stdout line 1
stdout line 2
stdout line 3
stdout line 4
stdout line 5
stdout line 6
stdout line 7
stdout line 8
stdout line 9
stdout line 10
]]>
</head>
<tail
  display_lines="16-25"
><![CDATA[stdout line 16
stdout line 17
stdout line 18
stdout line 19
stdout line 20
stdout line 21
stdout line 22
stdout line 23
stdout line 24
stdout line 25
]]>
</tail>
</stdout>
</shell_output>
