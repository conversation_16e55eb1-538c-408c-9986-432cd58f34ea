---
source: crates/forge_app/src/operation.rs
expression: to_value(actual)
---
<shell_output
  command="line_test_command"
  shell="/bin/bash"
  exit_code="0"
>
<stdout
  total_lines="15"
  full_output="/tmp/stdout_content.txt"
><![CDATA[stdout 1
stdout 2
stdout 3
stdout 4
stdout 5
stdout 6
stdout 7
stdout 8
stdout 9
stdout 10
stdout 11
stdout 12
stdout 13
stdout 14
stdout 15]]>
</stdout>
<stderr
  total_lines="12"
  full_output="/tmp/stderr_content.txt"
><![CDATA[stderr 1
stderr 2
stderr 3
stderr 4
stderr 5
stderr 6
stderr 7
stderr 8
stderr 9
stderr 10
stderr 11
stderr 12]]>
</stderr>
</shell_output>
