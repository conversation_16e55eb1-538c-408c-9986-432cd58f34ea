---
source: crates/forge_app/src/operation.rs
expression: to_value(actual)
---
<task_list
  total_tasks="15"
  pending_tasks="10"
  in_progress_tasks="2"
  done_tasks="3"
>
<task
  id="1"
  status="DONE"
><![CDATA[Task number 1]]>
</task>
<task
  id="2"
  status="IN_PROGRESS"
><![CDATA[Task number 2]]>
</task>
<task
  id="3"
  status="IN_PROGRESS"
><![CDATA[Task number 3]]>
</task>
<task
  id="4"
  status="PENDING"
><![CDATA[Task number 4]]>
</task>
<task
  id="5"
  status="DONE"
><![CDATA[Task number 5]]>
</task>
<task
  id="6"
  status="PENDING"
><![CDATA[Task number 6]]>
</task>
<task
  id="7"
  status="PENDING"
><![CDATA[Task number 7]]>
</task>
<task
  id="8"
  status="PENDING"
><![CDATA[Task number 8]]>
</task>
<task
  id="9"
  status="PENDING"
><![CDATA[Task number 9]]>
</task>
<task
  id="10"
  status="DONE"
><![CDATA[Task number 10]]>
</task>
<task
  id="11"
  status="PENDING"
><![CDATA[Task number 11]]>
</task>
<task
  id="12"
  status="PENDING"
><![CDATA[Task number 12]]>
</task>
<task
  id="13"
  status="PENDING"
><![CDATA[Task number 13]]>
</task>
<task
  id="14"
  status="PENDING"
><![CDATA[Task number 14]]>
</task>
<task
  id="15"
  status="PENDING"
><![CDATA[Task number 15]]>
</task>
</task_list>
