[package]
name = "forge_app"
version = "0.1.0"
edition = "2024"

[dependencies]
forge_domain.workspace = true
forge_stream.workspace = true
async-trait.workspace = true
anyhow.workspace = true
serde.workspace = true
futures.workspace = true
tracing.workspace = true
async-recursion.workspace = true
chrono.workspace = true
derive_setters.workspace = true
forge_walker.workspace = true
handlebars.workspace = true
rust-embed.workspace = true
serde_json.workspace = true
tokio.workspace = true
regex.workspace = true
thiserror.workspace = true
forge_display.workspace = true
console.workspace = true
derive_more.workspace = true
tempfile.workspace = true
strum.workspace = true
forge_template.workspace = true
merge.workspace = true
convert_case.workspace = true
backon.workspace = true
eserde.workspace = true
url.workspace = true

[dev-dependencies]
pretty_assertions.workspace = true
insta.workspace = true
