#!/usr/bin/env python3
"""
Test script to verify headers and make a basic request to Forge API
"""

import json
import requests
from forge_rotating_client import ForgeClient, TokenManager

def test_headers():
    """Test that headers match the official client."""
    print("Testing header generation...")
    
    client = ForgeClient()
    token = "test-token"
    headers = client._get_headers(token)
    
    print("Generated headers:")
    for key, value in headers.items():
        print(f"  {key}: {value}")
    
    # Verify critical headers match official client
    expected_headers = {
        'User-Agent': 'Forge',
        'X-Title': 'forge',
        'x-app-version': 'v0.1.0',
        'HTTP-Referer': 'https://forgecode.dev',
        'Connection': 'keep-alive',
    }
    
    print("\nHeader verification:")
    for key, expected_value in expected_headers.items():
        actual_value = headers.get(key)
        status = "✓" if actual_value == expected_value else "✗"
        print(f"  {status} {key}: {actual_value} (expected: {expected_value})")

def test_token_loading():
    """Test token loading and rotation."""
    print("\nTesting token loading...")
    
    token_manager = TokenManager("/home/<USER>/workspace/forge/proxy_config.json")
    print(f"Loaded {len(token_manager.tokens)} tokens")
    
    for i in range(min(5, len(token_manager.tokens) * 2)):
        token = token_manager.get_next_token()
        if token:
            print(f"  Token {i+1}: ...{token[-8:]}")
        else:
            print(f"  Token {i+1}: None")

def test_basic_request():
    """Test making a basic request to Forge API."""
    print("\nTesting basic API request...")
    
    try:
        client = ForgeClient()
        
        # Get a token
        token = client.token_manager.get_next_token()
        if not token:
            print("✗ No tokens available")
            return
        
        print(f"Using token: ...{token[-8:]}")
        
        # Prepare a simple request
        endpoint = f"{client.base_url}/chat/completions"
        headers = client._get_headers(token)
        proxy = client.proxy_manager.get_next_proxy()
        
        payload = {
            "model": "anthropic/claude-sonnet-4",
            "messages": [{"role": "user", "content": "Hello"}],
            "stream": False,
            "max_tokens": 10,
            "temperature": 0.7
        }
        
        print(f"Making request to: {endpoint}")
        print(f"Using proxy: {bool(proxy)}")
        
        response = requests.post(
            endpoint,
            json=payload,
            headers=headers,
            proxies=proxy,
            timeout=30
        )
        
        print(f"Response status: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print("✓ Request successful!")
            print(f"Response: {json.dumps(result, indent=2)[:500]}...")
        else:
            print(f"✗ Request failed: {response.status_code}")
            print(f"Response text: {response.text[:500]}...")
            
    except Exception as e:
        print(f"✗ Request failed with exception: {e}")

def main():
    """Run all tests."""
    print("Forge Client Header and Request Test")
    print("=" * 50)
    
    test_headers()
    test_token_loading()
    test_basic_request()
    
    print("\nTest completed!")

if __name__ == "__main__":
    main()
