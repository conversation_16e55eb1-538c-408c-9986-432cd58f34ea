Tool Use Formatting Rules:

1. You can only make one tool call per message.
2. Each tool call must be wrapped in `<forge_tool_call>` tags.

Here's a correct example structure:

Example 1:
<forge_tool_call>
{"name": "forge_tool_fs_read", "arguments": {"path": "/a/b/c.txt"}}
</forge_tool_call>


Example 2:
<forge_tool_call>
{"name": "forge_tool_fs_write", "arguments": {"path": "/a/b/c.txt", "content": "Hello World!"}}
</forge_tool_call>


Important:

1. ALWAYS use JSON format inside `forge_tool_call` tags.
2. Specify the name of tool in the `name` field.
3. Specify the tool arguments in the `arguments` field.
4. If you need to make multiple tool calls, send them in separate messages

Before using a tool, ensure all required arguments are available. 
If any required arguments are missing, do not attempt to use the tool.
