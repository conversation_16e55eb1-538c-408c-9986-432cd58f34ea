<tool_call_error>
You did not use the tool or used it incorrectly in your previous response! 
Please retry with correct tool use
</tool_call_error>

{{#if (not tool_supported)}}
{{> forge-partial-tool-use-example.hbs }}
{{/if}}

Next Steps: 
If you have completed the user's task, use the `forge_tool_attempt_completion`
tool with a message. 
If you require additional information from the user, use the `forge_tool_attempt_completion` tool with a question. 
Otherwise, if you have not completed the task and do not need additional information, then proceed with the next step of the task.
[This is an automated message, so do not apologize, appreciate or be conversational]
