# yaml-language-server: $schema=./forge.schema.json
variables:
  operating_agent: Forge
  # Define model anchors with simpler, purpose-based names
  advanced_model: &advanced_model anthropic/claude-sonnet-4

max_requests_per_turn: 50
max_tool_failure_per_turn: 3
top_p: 0.8
top_k: 30
max_tokens: 20480
max_walker_depth: 1

# Global compact configuration applied to all agents
compact:
  max_tokens: 2000
  token_threshold: 100000
  model: *advanced_model
  retention_window: 6
  message_threshold: 200
  eviction_window: 0.2
  prompt: "{{> forge-system-prompt-context-summarizer.hbs }}"
  on_turn_end: false

updates:
  frequency: "daily"
  auto_update: false

agents:
  - id: forge
    title: "Implementation focussed"
    description: |-
      Hands-on implementation agent that executes software development tasks through a structured 4-phase approach: task analysis, solution strategy, implementation, and quality assurance. Makes actual changes to codebases, runs shell commands, creates/modifies files, installs dependencies, and performs concrete development work. Use for building features, fixing bugs, refactoring code, or any task requiring actual modifications. Do not use for analysis-only tasks or when you want to explore options without making changes. Always validates changes through compilation and testing.
    model: *advanced_model
    system_prompt: |-
      {{> forge-system-prompt-engineer-act.hbs }}
    user_prompt: |-
      {{#if (eq event.name 'forge/user_task_update')}}
      <feedback>{{event.value}}</feedback>
      {{else}}
      <task>{{event.value}}</task>
      {{/if}}
    reasoning:
      enabled: true
    tools:
      - forge_tool_fs_read
      - forge_tool_fs_create
      - forge_tool_fs_remove
      - forge_tool_fs_patch
      - forge_tool_process_shell
      - forge_tool_net_fetch
      - forge_tool_fs_search
      - forge_tool_fs_undo

  - id: muse
    title: "Analysis and planning focussed"
    description: |-
      Strategic planning agent that analyzes codebases and creates comprehensive implementation plans without making any actual changes. Examines project structure, identifies risks, creates detailed Markdown documentation in the plans/ directory with objectives, implementation steps, and verification criteria. Use for project analysis, architectural guidance, risk assessment, or pre-implementation planning. Do not use when you need actual code changes or immediate implementation. Provides advisory recommendations and strategic roadmaps only.
    model: *advanced_model
    system_prompt: |-
      {{> forge-system-prompt-engineer-plan.hbs }}
    user_prompt: |-
      {{#if (eq event.name 'muse/user_task_update')}}
      <feedback>{{event.value}}</feedback>
      {{else}}
      <task>{{event.value}}</task>
      {{/if}}
      Only create new plans or update existing ones.
      Do not modify, create, or delete any code files.
    tools:
      - forge_tool_fs_read
      - forge_tool_net_fetch
      - forge_tool_fs_search
      - forge_tool_fs_create
      - forge_tool_fs_patch
