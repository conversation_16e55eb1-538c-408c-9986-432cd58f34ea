#!/usr/bin/env python3
"""
Forge Dev LLM Rotating Token Client

A standalone Python script that implements a rotating token system for accessing
Forge Dev LLMs without using the official Forge client. Features include:

- Token rotation (round-robin/random) for multiple FORGE_KEY values
- Proxy integration for IP address masking
- Direct API calls to Forge endpoints
- Complete telemetry disabling
- Usage tracking and daily limits
- Comprehensive error handling and logging

Usage:
    python forge_rotating_client.py --message "Hello, world!" --model "anthropic/claude-sonnet-4"
"""

import json
import logging
import os
import random
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any, Iterator
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry


class TokenManager:
    """Manages rotation and usage tracking of Forge API tokens."""
    
    def __init__(self, config_path: str):
        self.config_path = config_path
        self.tokens = []
        self.current_index = 0
        self.load_config()
    
    def load_config(self) -> None:
        """Load token configuration from JSON file."""
        try:
            with open(self.config_path, 'r') as f:
                config = json.load(f)
                self.tokens = config.get('tokens', [])
            logging.info(f"Loaded {len(self.tokens)} tokens from config")
        except Exception as e:
            logging.error(f"Failed to load config: {e}")
            raise
    
    def save_config(self) -> None:
        """Save updated token usage back to config file."""
        try:
            with open(self.config_path, 'r') as f:
                config = json.load(f)
            
            config['tokens'] = self.tokens
            
            with open(self.config_path, 'w') as f:
                json.dump(config, f, indent=2)
            logging.debug("Saved token usage to config")
        except Exception as e:
            logging.error(f"Failed to save config: {e}")
    
    def reset_daily_usage(self) -> None:
        """Reset daily usage counters if it's a new day."""
        today = datetime.now().strftime("%Y-%m-%d")
        for token in self.tokens:
            if token.get('last_reset') != today:
                token['used_today'] = 0
                token['last_reset'] = today
                logging.info(f"Reset daily usage for token ending in ...{token['token'][-8:]}")
    
    def get_next_token(self, strategy: str = "round_robin") -> Optional[str]:
        """Get the next available token using specified rotation strategy."""
        self.reset_daily_usage()
        
        # Filter active tokens that haven't exceeded daily limit
        available_tokens = [
            (i, token) for i, token in enumerate(self.tokens)
            if token.get('is_active', True) and 
               token.get('used_today', 0) < token.get('daily_limit', 50)
        ]
        
        if not available_tokens:
            logging.error("No available tokens (all exceeded daily limits or inactive)")
            return None
        
        if strategy == "round_robin":
            # Find next token in round-robin order
            for _ in range(len(available_tokens)):
                idx, token = available_tokens[self.current_index % len(available_tokens)]
                self.current_index = (self.current_index + 1) % len(available_tokens)
                return token['token']
        
        elif strategy == "random":
            # Select random available token
            idx, token = random.choice(available_tokens)
            return token['token']
        
        return None
    
    def mark_token_used(self, token: str) -> None:
        """Mark a token as used and increment usage counter."""
        for t in self.tokens:
            if t['token'] == token:
                t['used_today'] = t.get('used_today', 0) + 1
                logging.debug(f"Token usage: {t['used_today']}/{t.get('daily_limit', 50)}")
                break
        self.save_config()
    
    def mark_token_failed(self, token: str) -> None:
        """Mark a token as failed (potentially invalid)."""
        for t in self.tokens:
            if t['token'] == token:
                t['is_active'] = False
                logging.warning(f"Marked token as inactive: ...{token[-8:]}")
                break
        self.save_config()


class ProxyManager:
    """Manages proxy rotation for IP address masking."""
    
    def __init__(self, config_path: str):
        self.config_path = config_path
        self.proxies = []
        self.current_index = 0
        self.load_config()
    
    def load_config(self) -> None:
        """Load proxy configuration from JSON file."""
        try:
            with open(self.config_path, 'r') as f:
                config = json.load(f)
                self.proxies = config.get('proxies', [])
            logging.info(f"Loaded {len(self.proxies)} proxies from config")
        except Exception as e:
            logging.error(f"Failed to load proxy config: {e}")
            self.proxies = []
    
    def get_next_proxy(self) -> Optional[Dict[str, str]]:
        """Get the next available proxy in rotation."""
        if not self.proxies:
            return None
        
        active_proxies = [p for p in self.proxies if p.get('is_active', True)]
        if not active_proxies:
            return None
        
        proxy = active_proxies[self.current_index % len(active_proxies)]
        self.current_index = (self.current_index + 1) % len(active_proxies)
        
        # Format proxy for requests library
        proxy_url = proxy['url']
        if proxy.get('username') and proxy.get('password'):
            # Insert auth into URL
            protocol = proxy_url.split('://')[0]
            host_port = proxy_url.split('://')[1]
            proxy_url = f"{protocol}://{proxy['username']}:{proxy['password']}@{host_port}"
        
        return {
            'http': proxy_url,
            'https': proxy_url
        }


class TelemetryDisabler:
    """Utilities to disable telemetry and tracking."""
    
    @staticmethod
    def disable_telemetry() -> None:
        """Set environment variables to disable telemetry."""
        os.environ['FORGE_TRACKER'] = 'false'
        os.environ['DO_NOT_TRACK'] = '1'
        os.environ['TELEMETRY_DISABLED'] = 'true'
        logging.info("Telemetry disabled via environment variables")
    
    @staticmethod
    def get_minimal_headers() -> Dict[str, str]:
        """Get minimal headers to avoid fingerprinting."""
        return {
            'Content-Type': 'application/json',
            'Accept': 'text/event-stream',
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
        }


class ForgeClient:
    """Main client for interacting with Forge Dev LLM API."""
    
    def __init__(self, config_path: str = "/home/<USER>/workspace/forge/proxy_config.json"):
        self.config_path = config_path
        self.base_url = "https://api.forgecode.dev/api/v1"
        self.token_manager = TokenManager(config_path)
        self.proxy_manager = ProxyManager(config_path)
        
        # Disable telemetry
        TelemetryDisabler.disable_telemetry()
        
        # Setup session with retry strategy
        self.session = requests.Session()
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
        
        logging.info("ForgeClient initialized")
    
    def _get_headers(self, token: str) -> Dict[str, str]:
        """Get request headers with authentication."""
        headers = TelemetryDisabler.get_minimal_headers()
        headers.update({
            'Authorization': f'Bearer {token}',
            'X-Title': 'forge-proxy',
            'x-app-version': 'v1.0.0',
            'HTTP-Referer': 'https://github.com/antinomyhq/forge',
            'User-Agent': 'forge-rotating-client/1.0.0',
        })
        return headers

    def chat_completion(
        self,
        messages: List[Dict[str, str]],
        model: str = "anthropic/claude-sonnet-4",
        stream: bool = True,
        max_tokens: int = 4000,
        temperature: float = 0.7,
        max_retries: int = 3
    ) -> Iterator[Dict[str, Any]]:
        """
        Send a chat completion request to Forge API with token and proxy rotation.

        Args:
            messages: List of message dicts with 'role' and 'content'
            model: Model identifier
            stream: Whether to stream responses
            max_tokens: Maximum tokens in response
            temperature: Sampling temperature
            max_retries: Maximum retry attempts

        Yields:
            Dict containing response chunks or complete response
        """
        endpoint = f"{self.base_url}/chat/completions"

        # Prepare request payload
        payload = {
            "model": model,
            "messages": messages,
            "stream": stream,
            "max_tokens": max_tokens,
            "temperature": temperature,
            "stream_options": {"include_usage": True} if stream else None
        }

        for attempt in range(max_retries):
            # Get next token and proxy
            token = self.token_manager.get_next_token()
            if not token:
                raise Exception("No available tokens")

            proxy = self.proxy_manager.get_next_proxy()
            headers = self._get_headers(token)

            try:
                logging.info(f"Attempt {attempt + 1}: Using token ...{token[-8:]} with proxy {bool(proxy)}")

                response = self.session.post(
                    endpoint,
                    json=payload,
                    headers=headers,
                    proxies=proxy,
                    stream=stream,
                    timeout=30
                )

                if response.status_code == 200:
                    # Mark token as used
                    self.token_manager.mark_token_used(token)

                    if stream:
                        yield from self._parse_stream_response(response)
                    else:
                        yield response.json()
                    return

                elif response.status_code in [401, 403]:
                    # Invalid token
                    logging.warning(f"Token authentication failed: {response.status_code}")
                    self.token_manager.mark_token_failed(token)
                    continue

                elif response.status_code == 429:
                    # Rate limited
                    logging.warning("Rate limited, trying next token")
                    continue

                else:
                    logging.error(f"API error: {response.status_code} - {response.text}")
                    if attempt == max_retries - 1:
                        raise Exception(f"API request failed: {response.status_code}")

            except requests.exceptions.RequestException as e:
                logging.error(f"Request failed: {e}")
                if attempt == max_retries - 1:
                    raise
                time.sleep(2 ** attempt)  # Exponential backoff

        raise Exception("All retry attempts failed")

    def _parse_stream_response(self, response: requests.Response) -> Iterator[Dict[str, Any]]:
        """Parse Server-Sent Events from streaming response."""
        try:
            for line in response.iter_lines(decode_unicode=True):
                if line.startswith('data: '):
                    data = line[6:]  # Remove 'data: ' prefix

                    if data.strip() == '[DONE]':
                        break

                    try:
                        chunk = json.loads(data)
                        yield chunk
                    except json.JSONDecodeError:
                        logging.debug(f"Failed to parse JSON: {data}")
                        continue

        except Exception as e:
            logging.error(f"Error parsing stream: {e}")
            raise

    def simple_chat(self, message: str, model: str = "anthropic/claude-sonnet-4") -> str:
        """
        Simple chat interface that returns the complete response as a string.

        Args:
            message: User message
            model: Model to use

        Returns:
            Complete response text
        """
        messages = [{"role": "user", "content": message}]

        response_text = ""
        for chunk in self.chat_completion(messages, model=model, stream=True):
            if 'choices' in chunk and chunk['choices']:
                delta = chunk['choices'][0].get('delta', {})
                if 'content' in delta:
                    response_text += delta['content']

        return response_text.strip()


def setup_logging(level: str = "INFO") -> None:
    """Setup logging configuration."""
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('forge_rotating_client.log')
        ]
    )


def main():
    """Main CLI interface for testing the client."""
    import argparse

    parser = argparse.ArgumentParser(description="Forge Rotating Token Client")
    parser.add_argument("--message", "-m", required=True, help="Message to send")
    parser.add_argument("--model", default="anthropic/claude-sonnet-4", help="Model to use")
    parser.add_argument("--config", default="/home/<USER>/workspace/forge/proxy_config.json",
                       help="Path to proxy config file")
    parser.add_argument("--log-level", default="INFO", choices=["DEBUG", "INFO", "WARNING", "ERROR"],
                       help="Logging level")
    parser.add_argument("--stream", action="store_true", help="Stream response")

    args = parser.parse_args()

    setup_logging(args.log_level)

    try:
        client = ForgeClient(args.config)

        if args.stream:
            print("Streaming response:")
            print("-" * 50)
            for chunk in client.chat_completion([{"role": "user", "content": args.message}],
                                              model=args.model, stream=True):
                if 'choices' in chunk and chunk['choices']:
                    delta = chunk['choices'][0].get('delta', {})
                    if 'content' in delta:
                        print(delta['content'], end='', flush=True)
            print("\n" + "-" * 50)
        else:
            response = client.simple_chat(args.message, args.model)
            print("Response:")
            print("-" * 50)
            print(response)
            print("-" * 50)

    except Exception as e:
        logging.error(f"Error: {e}")
        print(f"Error: {e}")


if __name__ == "__main__":
    main()
