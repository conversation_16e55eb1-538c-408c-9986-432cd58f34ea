#!/usr/bin/env python3
"""
Forge Dev LLM Rotating Token Client

A minimal Python script that implements token rotation for accessing Forge Dev LLMs
while accurately mimicking the official Forge client's HTTP requests.

Features:
- Token rotation (round-robin) for multiple FORGE_KEY values
- Proxy integration for IP address masking
- Exact header replication from official Forge client
- Basic error handling and retry logic

Usage:
    python forge_rotating_client.py --message "Hello, world!"
"""

import json
import logging
import time
from typing import Dict, List, Optional, Any
import requests


class TokenManager:
    """Manages rotation of Forge API tokens."""

    def __init__(self, config_path: str):
        self.config_path = config_path
        self.tokens = []
        self.current_index = 0
        self.load_config()

    def load_config(self) -> None:
        """Load token configuration from JSON file."""
        try:
            with open(self.config_path, 'r') as f:
                config = json.load(f)
                self.tokens = config.get('tokens', [])
            logging.info(f"Loaded {len(self.tokens)} tokens from config")
        except Exception as e:
            logging.error(f"Failed to load config: {e}")
            raise

    def get_next_token(self) -> Optional[str]:
        """Get the next available token using round-robin rotation."""
        # Filter active tokens
        available_tokens = [
            token for token in self.tokens
            if token.get('is_active', True)
        ]

        if not available_tokens:
            logging.error("No available tokens")
            return None

        # Round-robin rotation
        token = available_tokens[self.current_index % len(available_tokens)]
        self.current_index = (self.current_index + 1) % len(available_tokens)
        return token['token']


class ProxyManager:
    """Manages proxy rotation for IP address masking."""

    def __init__(self, config_path: str):
        self.config_path = config_path
        self.proxies = []
        self.current_index = 0
        self.load_config()

    def load_config(self) -> None:
        """Load proxy configuration from JSON file."""
        try:
            with open(self.config_path, 'r') as f:
                config = json.load(f)
                self.proxies = config.get('proxies', [])
            logging.info(f"Loaded {len(self.proxies)} proxies from config")
        except Exception as e:
            logging.error(f"Failed to load proxy config: {e}")
            self.proxies = []

    def get_next_proxy(self) -> Optional[Dict[str, str]]:
        """Get the next available proxy in rotation."""
        if not self.proxies:
            return None

        active_proxies = [p for p in self.proxies if p.get('is_active', True)]
        if not active_proxies:
            return None

        proxy = active_proxies[self.current_index % len(active_proxies)]
        self.current_index = (self.current_index + 1) % len(active_proxies)

        # Format proxy for requests library
        proxy_url = proxy['url']
        if proxy.get('username') and proxy.get('password'):
            # Insert auth into URL
            protocol = proxy_url.split('://')[0]
            host_port = proxy_url.split('://')[1]
            proxy_url = f"{protocol}://{proxy['username']}:{proxy['password']}@{host_port}"

        return {
            'http': proxy_url,
            'https': proxy_url
        }


class ForgeClient:
    """Main client for interacting with Forge Dev LLM API."""

    def __init__(self, config_path: str = "/home/<USER>/workspace/forge/proxy_config.json"):
        self.config_path = config_path
        self.base_url = "https://api.forgecode.dev/api/v1"
        self.token_manager = TokenManager(config_path)
        self.proxy_manager = ProxyManager(config_path)
        self.session = requests.Session()
        logging.info("ForgeClient initialized")

    def _get_headers(self, token: str) -> Dict[str, str]:
        """Get request headers that exactly match the official Forge client."""
        # Based on analysis of official Forge client code:
        # ForgeProvider (openai/provider.rs) uses these exact headers for chat completions:
        # - Authorization: Bearer {api_key}
        # - X-Title: "forge"
        # - x-app-version: "v{version}" (defaults to "v0.1.0-dev")
        # - HTTP-Referer: "https://github.com/antinomyhq/forge"
        return {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json',
            'X-Title': 'forge',
            'x-app-version': 'v0.1.0-dev',
            'HTTP-Referer': 'https://github.com/antinomyhq/forge',
        }


    def chat_completion(
        self,
        messages: List[Dict[str, str]],
        model: str = "anthropic/claude-sonnet-4",
        max_retries: int = 3
    ) -> str:
        """
        Send a chat completion request to Forge API with token and proxy rotation.

        Args:
            messages: List of message dicts with 'role' and 'content'
            model: Model identifier
            max_retries: Maximum retry attempts

        Returns:
            Complete response text
        """
        endpoint = f"{self.base_url}/chat/completions"

        # Prepare request payload (non-streaming for simplicity)
        payload = {
            "model": model,
            "messages": messages,
            "stream": False,
            "max_tokens": 4000,
            "temperature": 0.7
        }

        for attempt in range(max_retries):
            # Get next token and proxy
            token = self.token_manager.get_next_token()
            if not token:
                raise Exception("No available tokens")

            proxy = self.proxy_manager.get_next_proxy()
            headers = self._get_headers(token)

            try:
                logging.info(f"Attempt {attempt + 1}: Using token ...{token[-8:]} with proxy {bool(proxy)}")

                response = self.session.post(
                    endpoint,
                    json=payload,
                    headers=headers,
                    proxies=proxy,
                    timeout=60
                )

                if response.status_code == 200:
                    result = response.json()
                    if 'choices' in result and result['choices']:
                        content = result['choices'][0].get('message', {}).get('content', '')
                        return content.strip()
                    return ""

                elif response.status_code in [401, 403]:
                    # Invalid token
                    logging.warning(f"Token authentication failed: {response.status_code}")
                    continue

                elif response.status_code == 429:
                    # Rate limited
                    logging.warning("Rate limited, trying next token")
                    continue

                else:
                    logging.error(f"API error: {response.status_code} - {response.text}")
                    if attempt == max_retries - 1:
                        raise Exception(f"API request failed: {response.status_code}")

            except requests.exceptions.RequestException as e:
                logging.error(f"Request failed: {e}")
                if attempt == max_retries - 1:
                    raise
                time.sleep(2 ** attempt)  # Exponential backoff

        raise Exception("All retry attempts failed")

    def simple_chat(self, message: str, model: str = "anthropic/claude-sonnet-4") -> str:
        """Simple chat interface."""
        messages = [{"role": "user", "content": message}]
        return self.chat_completion(messages, model=model)


def setup_logging(level: str = "INFO") -> None:
    """Setup logging configuration."""
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler()]
    )


def main():
    """Main CLI interface for testing the client."""
    import argparse

    parser = argparse.ArgumentParser(description="Forge Rotating Token Client")
    parser.add_argument("--message", "-m", required=True, help="Message to send")
    parser.add_argument("--model", default="anthropic/claude-sonnet-4", help="Model to use")
    parser.add_argument("--config", default="/home/<USER>/workspace/forge/proxy_config.json",
                       help="Path to proxy config file")
    parser.add_argument("--log-level", default="INFO", choices=["DEBUG", "INFO", "WARNING", "ERROR"],
                       help="Logging level")

    args = parser.parse_args()

    setup_logging(args.log_level)

    try:
        client = ForgeClient(args.config)
        response = client.simple_chat(args.message, args.model)
        print("Response:")
        print("-" * 50)
        print(response)
        print("-" * 50)

    except Exception as e:
        logging.error(f"Error: {e}")
        print(f"Error: {e}")


if __name__ == "__main__":
    main()
