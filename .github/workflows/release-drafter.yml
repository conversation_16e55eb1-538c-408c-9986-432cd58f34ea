# -------------------------------------------------------------------
# ------------------------------- WARNING ---------------------------
# -------------------------------------------------------------------
#
# This file was automatically generated by gh-workflows using the
# gh-workflow-gen bin. You should add and commit this file to your
# git repository. **DO NOT EDIT THIS FILE BY HAND!** Any manual changes
# will be lost if the file is regenerated.
#
# To make modifications, update your `build.rs` configuration to adjust
# the workflow description as needed, then regenerate this file to apply
# those changes.
#
# -------------------------------------------------------------------
# ----------------------------- END WARNING -------------------------
# -------------------------------------------------------------------

name: Release Drafter
on:
  pull_request_target:
    types:
    - opened
    - reopened
    - synchronize
    branches:
    - main
  push:
    branches:
    - main
permissions:
  contents: write
  pull-requests: write
jobs:
  update_release_draft:
    name: update_release_draft
    runs-on: ubuntu-latest
    steps:
    - uses: release-drafter/release-drafter@v6
      with:
        config-name: release-drafter.yml
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
