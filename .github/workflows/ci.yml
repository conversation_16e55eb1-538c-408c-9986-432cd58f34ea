# -------------------------------------------------------------------
# ------------------------------- WARNING ---------------------------
# -------------------------------------------------------------------
#
# This file was automatically generated by gh-workflows using the
# gh-workflow-gen bin. You should add and commit this file to your
# git repository. **DO NOT EDIT THIS FILE BY HAND!** Any manual changes
# will be lost if the file is regenerated.
#
# To make modifications, update your `build.rs` configuration to adjust
# the workflow description as needed, then regenerate this file to apply
# those changes.
#
# -------------------------------------------------------------------
# ----------------------------- END WARNING -------------------------
# -------------------------------------------------------------------

name: ci
env:
  RUSTFLAGS: -Dwarnings
  OPENROUTER_API_KEY: ${{secrets.OPENROUTER_API_KEY}}
on:
  pull_request:
    types:
    - opened
    - synchronize
    - reopened
    branches:
    - main
  push:
    branches:
    - main
    tags:
    - v*
jobs:
  build:
    name: Build and Test
    runs-on: ubuntu-latest
    permissions:
      contents: read
    steps:
    - name: Checkout Code
      uses: actions/checkout@v4
    - name: Setup Rust Toolchain
      uses: actions-rust-lang/setup-rust-toolchain@v1
      with:
        toolchain: stable
    - name: Install nextest
      run: cargo install cargo-nextest --locked
    - name: Cargo Nextest
      run: cargo nextest run --all-features --workspace
  lint:
    name: Lint
    runs-on: ubuntu-latest
    permissions:
      contents: read
    steps:
    - name: Checkout Code
      uses: actions/checkout@v4
    - name: Setup Rust Toolchain
      uses: actions-rust-lang/setup-rust-toolchain@v1
      with:
        toolchain: nightly
        components: clippy, rustfmt
        cache: true
        cache-directories: |-
          ~/.cargo/registry
          ~/.cargo/git
          target
    - name: Cargo Fmt
      run: cargo +nightly fmt --all --check
    - name: Cargo Clippy
      run: cargo +nightly clippy --all-features --workspace -- -D warnings
  draft_release:
    needs:
    - build
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    name: Draft Release
    runs-on: ubuntu-latest
    permissions:
      contents: write
      pull-requests: write
    steps:
    - uses: actions/checkout@v4
    - id: create_release
      uses: release-drafter/release-drafter@v6
      with:
        config-name: release-drafter.yml
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
    - id: set_output
      run: echo "crate_release_id=${{ steps.create_release.outputs.id }}" >> $GITHUB_OUTPUT && echo "crate_release_name=${{ steps.create_release.outputs.tag_name }}" >> $GITHUB_OUTPUT
    outputs:
      crate_release_name: ${{ steps.set_output.outputs.crate_release_name }}
      crate_release_id: ${{ steps.set_output.outputs.crate_release_id }}
  build_release:
    needs:
    - draft_release
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    name: build-release
    runs-on: ${{ matrix.os }}
    permissions:
      contents: write
      pull-requests: write
    strategy:
      matrix:
        include:
        - binary_name: forge-x86_64-unknown-linux-musl
          binary_path: target/x86_64-unknown-linux-musl/release/forge
          cross: 'true'
          os: ubuntu-latest
          target: x86_64-unknown-linux-musl
        - binary_name: forge-aarch64-unknown-linux-musl
          binary_path: target/aarch64-unknown-linux-musl/release/forge
          cross: 'true'
          os: ubuntu-latest
          target: aarch64-unknown-linux-musl
        - binary_name: forge-x86_64-unknown-linux-gnu
          binary_path: target/x86_64-unknown-linux-gnu/release/forge
          cross: 'false'
          os: ubuntu-latest
          target: x86_64-unknown-linux-gnu
        - binary_name: forge-aarch64-unknown-linux-gnu
          binary_path: target/aarch64-unknown-linux-gnu/release/forge
          cross: 'true'
          os: ubuntu-latest
          target: aarch64-unknown-linux-gnu
        - binary_name: forge-x86_64-apple-darwin
          binary_path: target/x86_64-apple-darwin/release/forge
          cross: 'false'
          os: macos-latest
          target: x86_64-apple-darwin
        - binary_name: forge-aarch64-apple-darwin
          binary_path: target/aarch64-apple-darwin/release/forge
          cross: 'false'
          os: macos-latest
          target: aarch64-apple-darwin
        - binary_name: forge-x86_64-pc-windows-msvc.exe
          binary_path: target/x86_64-pc-windows-msvc/release/forge.exe
          cross: 'false'
          os: windows-latest
          target: x86_64-pc-windows-msvc
        - binary_name: forge-aarch64-pc-windows-msvc.exe
          binary_path: target/aarch64-pc-windows-msvc/release/forge.exe
          cross: 'false'
          os: windows-latest
          target: aarch64-pc-windows-msvc
    steps:
    - uses: actions/checkout@v4
    - if: ${{ matrix.cross == 'false' }}
      uses: taiki-e/setup-cross-toolchain-action@v1
      with:
        target: ${{ matrix.target }}
    - name: Add Rust target
      run: rustup target add ${{ matrix.target }}
    - if: '!contains(matrix.target, ''-unknown-linux-'')'
      run: echo "RUSTFLAGS=-C target-feature=+crt-static" >> $GITHUB_ENV
    - uses: ClementTsang/cargo-action@v0.0.6
      with:
        command: build --release
        args: --target ${{ matrix.target }}
        use-cross: ${{ matrix.cross }}
        cross-version: 0.2.4
      env:
        RUSTFLAGS: ${{ env.RUSTFLAGS }}
        POSTHOG_API_SECRET: ${{secrets.POSTHOG_API_SECRET}}
        APP_VERSION: ${{ needs.draft_release.outputs.crate_release_name }}
    - run: cp ${{ matrix.binary_path }} ${{ matrix.binary_name }}
    - uses: xresloader/upload-to-github-release@v1
      with:
        release_id: ${{ needs.draft_release.outputs.crate_release_id }}
        file: ${{ matrix.binary_name }}
        overwrite: 'true'
  build_release_pr:
    needs:
    - draft_release
    if: github.event_name == 'pull_request' && contains(github.event.pull_request.labels.*.name, 'build-all-targets')
    name: build-release
    runs-on: ${{ matrix.os }}
    permissions:
      contents: write
      pull-requests: write
    strategy:
      matrix:
        include:
        - binary_name: forge-x86_64-unknown-linux-musl
          binary_path: target/x86_64-unknown-linux-musl/release/forge
          cross: 'true'
          os: ubuntu-latest
          target: x86_64-unknown-linux-musl
        - binary_name: forge-aarch64-unknown-linux-musl
          binary_path: target/aarch64-unknown-linux-musl/release/forge
          cross: 'true'
          os: ubuntu-latest
          target: aarch64-unknown-linux-musl
        - binary_name: forge-x86_64-unknown-linux-gnu
          binary_path: target/x86_64-unknown-linux-gnu/release/forge
          cross: 'false'
          os: ubuntu-latest
          target: x86_64-unknown-linux-gnu
        - binary_name: forge-aarch64-unknown-linux-gnu
          binary_path: target/aarch64-unknown-linux-gnu/release/forge
          cross: 'true'
          os: ubuntu-latest
          target: aarch64-unknown-linux-gnu
        - binary_name: forge-x86_64-apple-darwin
          binary_path: target/x86_64-apple-darwin/release/forge
          cross: 'false'
          os: macos-latest
          target: x86_64-apple-darwin
        - binary_name: forge-aarch64-apple-darwin
          binary_path: target/aarch64-apple-darwin/release/forge
          cross: 'false'
          os: macos-latest
          target: aarch64-apple-darwin
        - binary_name: forge-x86_64-pc-windows-msvc.exe
          binary_path: target/x86_64-pc-windows-msvc/release/forge.exe
          cross: 'false'
          os: windows-latest
          target: x86_64-pc-windows-msvc
        - binary_name: forge-aarch64-pc-windows-msvc.exe
          binary_path: target/aarch64-pc-windows-msvc/release/forge.exe
          cross: 'false'
          os: windows-latest
          target: aarch64-pc-windows-msvc
    steps:
    - uses: actions/checkout@v4
    - if: ${{ matrix.cross == 'false' }}
      uses: taiki-e/setup-cross-toolchain-action@v1
      with:
        target: ${{ matrix.target }}
    - name: Add Rust target
      run: rustup target add ${{ matrix.target }}
    - if: '!contains(matrix.target, ''-unknown-linux-'')'
      run: echo "RUSTFLAGS=-C target-feature=+crt-static" >> $GITHUB_ENV
    - uses: ClementTsang/cargo-action@v0.0.6
      with:
        command: build --release
        args: --target ${{ matrix.target }}
        use-cross: ${{ matrix.cross }}
        cross-version: 0.2.4
      env:
        RUSTFLAGS: ${{ env.RUSTFLAGS }}
        POSTHOG_API_SECRET: ${{secrets.POSTHOG_API_SECRET}}
        APP_VERSION: ${{ needs.draft_release.outputs.crate_release_name }}
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
