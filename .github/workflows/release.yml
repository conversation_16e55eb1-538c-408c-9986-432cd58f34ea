# -------------------------------------------------------------------
# ------------------------------- WARNING ---------------------------
# -------------------------------------------------------------------
#
# This file was automatically generated by gh-workflows using the
# gh-workflow-gen bin. You should add and commit this file to your
# git repository. **DO NOT EDIT THIS FILE BY HAND!** Any manual changes
# will be lost if the file is regenerated.
#
# To make modifications, update your `build.rs` configuration to adjust
# the workflow description as needed, then regenerate this file to apply
# those changes.
#
# -------------------------------------------------------------------
# ----------------------------- END WARNING -------------------------
# -------------------------------------------------------------------

name: Multi Channel Release
on:
  release:
    types:
    - published
permissions:
  contents: write
  pull-requests: write
jobs:
  build_release:
    name: build-release
    runs-on: ${{ matrix.os }}
    permissions:
      contents: write
      pull-requests: write
    strategy:
      matrix:
        include:
        - binary_name: forge-x86_64-unknown-linux-musl
          binary_path: target/x86_64-unknown-linux-musl/release/forge
          cross: 'true'
          os: ubuntu-latest
          target: x86_64-unknown-linux-musl
        - binary_name: forge-aarch64-unknown-linux-musl
          binary_path: target/aarch64-unknown-linux-musl/release/forge
          cross: 'true'
          os: ubuntu-latest
          target: aarch64-unknown-linux-musl
        - binary_name: forge-x86_64-unknown-linux-gnu
          binary_path: target/x86_64-unknown-linux-gnu/release/forge
          cross: 'false'
          os: ubuntu-latest
          target: x86_64-unknown-linux-gnu
        - binary_name: forge-aarch64-unknown-linux-gnu
          binary_path: target/aarch64-unknown-linux-gnu/release/forge
          cross: 'true'
          os: ubuntu-latest
          target: aarch64-unknown-linux-gnu
        - binary_name: forge-x86_64-apple-darwin
          binary_path: target/x86_64-apple-darwin/release/forge
          cross: 'false'
          os: macos-latest
          target: x86_64-apple-darwin
        - binary_name: forge-aarch64-apple-darwin
          binary_path: target/aarch64-apple-darwin/release/forge
          cross: 'false'
          os: macos-latest
          target: aarch64-apple-darwin
        - binary_name: forge-x86_64-pc-windows-msvc.exe
          binary_path: target/x86_64-pc-windows-msvc/release/forge.exe
          cross: 'false'
          os: windows-latest
          target: x86_64-pc-windows-msvc
        - binary_name: forge-aarch64-pc-windows-msvc.exe
          binary_path: target/aarch64-pc-windows-msvc/release/forge.exe
          cross: 'false'
          os: windows-latest
          target: aarch64-pc-windows-msvc
    steps:
    - uses: actions/checkout@v4
    - if: ${{ matrix.cross == 'false' }}
      uses: taiki-e/setup-cross-toolchain-action@v1
      with:
        target: ${{ matrix.target }}
    - name: Add Rust target
      run: rustup target add ${{ matrix.target }}
    - if: '!contains(matrix.target, ''-unknown-linux-'')'
      run: echo "RUSTFLAGS=-C target-feature=+crt-static" >> $GITHUB_ENV
    - uses: ClementTsang/cargo-action@v0.0.6
      with:
        command: build --release
        args: --target ${{ matrix.target }}
        use-cross: ${{ matrix.cross }}
        cross-version: 0.2.4
      env:
        RUSTFLAGS: ${{ env.RUSTFLAGS }}
        POSTHOG_API_SECRET: ${{secrets.POSTHOG_API_SECRET}}
        APP_VERSION: ${{ github.event.release.tag_name }}
    - run: cp ${{ matrix.binary_path }} ${{ matrix.binary_name }}
    - uses: xresloader/upload-to-github-release@v1
      with:
        release_id: ${{ github.event.release.id }}
        file: ${{ matrix.binary_name }}
        overwrite: 'true'
  npm_release:
    needs:
    - build_release
    name: npm_release
    runs-on: ubuntu-latest
    strategy:
      matrix:
        repository:
        - antinomyhq/npm-code-forge
        - antinomyhq/npm-forgecode
    steps:
    - uses: actions/checkout@v4
      with:
        repository: ${{ matrix.repository }}
        ref: main
        token: ${{ secrets.NPM_ACCESS }}
    - run: ./update-package.sh ${{ github.event.release.tag_name }}
      env:
        AUTO_PUSH: 'true'
        CI: 'true'
        NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
  homebrew_release:
    needs:
    - build_release
    name: homebrew_release
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
      with:
        repository: antinomyhq/homebrew-code-forge
        ref: main
        token: ${{ secrets.HOMEBREW_ACCESS }}
    - run: GITHUB_TOKEN="${{ secrets.HOMEBREW_ACCESS }}" ./update-formula.sh ${{ github.event.release.tag_name }}
