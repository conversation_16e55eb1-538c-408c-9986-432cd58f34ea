# -------------------------------------------------------------------
# ------------------------------- WARNING ---------------------------
# -------------------------------------------------------------------
#
# This file was automatically generated by gh-workflows using the
# gh-workflow-gen bin. You should add and commit this file to your
# git repository. **DO NOT EDIT THIS FILE BY HAND!** Any manual changes
# will be lost if the file is regenerated.
#
# To make modifications, update your `build.rs` configuration to adjust
# the workflow description as needed, then regenerate this file to apply
# those changes.
#
# -------------------------------------------------------------------
# ----------------------------- END WARNING -------------------------
# -------------------------------------------------------------------

name: Github Label Sync
on:
  push:
    branches:
    - main
permissions:
  contents: write
jobs:
  label-sync:
    name: label-sync
    runs-on: ubuntu-latest
    permissions:
      issues: write
    steps:
    - name: Checkout
      uses: actions/checkout@v4
    - name: Sync labels
      run: |-
        npx github-label-sync \
          --access-token ${{ secrets.GITHUB_TOKEN }} \
          --labels ".github/labels.json" \
          ${{ github.repository }}
