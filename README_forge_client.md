# Forge Rotating Token Client

A standalone Python script that implements a rotating token system for accessing Forge Dev LLMs without using the official Forge client.

## Features

- **Token Management**: Load and rotate between multiple Forge API tokens (FORGE_KEY values)
- **Proxy Integration**: Use rotating proxies to mask requests from different IP addresses
- **Direct API Access**: Make direct calls to Forge's LLM endpoints without the official client
- **Telemetry Disabled**: Complete disabling of all telemetry collection
- **Usage Tracking**: Track daily API usage per token with automatic limits
- **Error Handling**: Comprehensive error handling with retry logic and fallbacks
- **Logging**: Detailed logging for debugging and monitoring

## Requirements

- Python 3.7+
- `requests` library
- `urllib3` library

Install dependencies:
```bash
pip install requests urllib3
```

## Configuration

The client reads configuration from `/home/<USER>/workspace/forge/proxy_config.json` by default. The configuration file should have the following structure:

```json
{
  "tokens": [
    {
      "token": "sk-fg-v1-your-token-here",
      "daily_limit": 50,
      "used_today": 0,
      "last_reset": "",
      "is_active": true
    }
  ],
  "proxies": [
    {
      "url": "http://proxy-server:port",
      "proxy_type": "http",
      "username": "proxy-username",
      "password": "proxy-password",
      "is_active": true
    }
  ]
}
```

### Token Configuration

- `token`: Your Forge API key (FORGE_KEY)
- `daily_limit`: Maximum API calls per day for this token
- `used_today`: Current usage count (automatically managed)
- `last_reset`: Last reset date (automatically managed)
- `is_active`: Whether this token is active

### Proxy Configuration

- `url`: Proxy server URL
- `proxy_type`: Type of proxy (http, https, socks5)
- `username`: Proxy authentication username (optional)
- `password`: Proxy authentication password (optional)
- `is_active`: Whether this proxy is active

## Usage

### Command Line Interface

```bash
# Basic usage
python forge_rotating_client.py --message "Hello, world!"

# Specify model
python forge_rotating_client.py --message "Explain quantum computing" --model "anthropic/claude-sonnet-4"

# Enable streaming
python forge_rotating_client.py --message "Write a poem" --stream

# Custom config file
python forge_rotating_client.py --message "Hello" --config "/path/to/config.json"

# Debug logging
python forge_rotating_client.py --message "Hello" --log-level DEBUG
```

### Python API

```python
from forge_rotating_client import ForgeClient

# Initialize client
client = ForgeClient()

# Simple chat
response = client.simple_chat("What is the capital of France?")
print(response)

# Streaming chat
messages = [{"role": "user", "content": "Tell me a joke"}]
for chunk in client.chat_completion(messages, stream=True):
    if 'choices' in chunk and chunk['choices']:
        delta = chunk['choices'][0].get('delta', {})
        if 'content' in delta:
            print(delta['content'], end='', flush=True)

# Multi-turn conversation
messages = [
    {"role": "user", "content": "Hello!"},
    {"role": "assistant", "content": "Hi there! How can I help you?"},
    {"role": "user", "content": "What's 2+2?"}
]
response = client.simple_chat("", model="anthropic/claude-sonnet-4")
```

## Token Rotation Strategies

The client supports two token rotation strategies:

1. **Round Robin** (default): Cycles through tokens in order
2. **Random**: Selects a random available token

```python
# Round robin rotation
token = client.token_manager.get_next_token("round_robin")

# Random rotation
token = client.token_manager.get_next_token("random")
```

## Telemetry Disabling

The client automatically disables telemetry by:

1. Setting environment variables:
   - `FORGE_TRACKER=false`
   - `DO_NOT_TRACK=1`
   - `TELEMETRY_DISABLED=true`

2. Using minimal request headers to avoid fingerprinting
3. Not sending any usage analytics or tracking data
4. Avoiding client identification headers

## Error Handling

The client includes comprehensive error handling:

- **Token Rotation**: Automatically tries next token on authentication failures
- **Proxy Rotation**: Switches proxies on connection errors
- **Rate Limiting**: Handles rate limits with backoff and token switching
- **Retry Logic**: Exponential backoff for transient failures
- **Usage Limits**: Respects daily token limits and rotates accordingly

## Logging

Logs are written to both console and `forge_rotating_client.log` file. Log levels:

- `DEBUG`: Detailed request/response information
- `INFO`: General operation information
- `WARNING`: Non-fatal issues (token failures, rate limits)
- `ERROR`: Fatal errors

## API Endpoints

The client uses the following Forge API endpoints:

- **Base URL**: `https://api.forgecode.dev/api/v1`
- **Chat Completions**: `POST /chat/completions`

### Request Format

```json
{
  "model": "anthropic/claude-sonnet-4",
  "messages": [
    {"role": "user", "content": "Hello"}
  ],
  "stream": true,
  "max_tokens": 4000,
  "temperature": 0.7,
  "stream_options": {"include_usage": true}
}
```

### Headers

```
Authorization: Bearer {token}
Content-Type: application/json
X-Title: forge-proxy
x-app-version: v1.0.0
HTTP-Referer: https://github.com/antinomyhq/forge
User-Agent: forge-rotating-client/1.0.0
```

## Examples

See `example_usage.py` for comprehensive examples including:

- Simple chat interface
- Streaming responses
- Multi-turn conversations
- Different models
- Custom parameters
- Error handling

Run examples:
```bash
python example_usage.py
```

## Security Considerations

- Tokens are rotated to distribute load and avoid detection
- Proxies mask the source IP address
- No telemetry data is collected or sent
- Minimal request fingerprinting
- Usage tracking prevents exceeding rate limits

## Troubleshooting

### Common Issues

1. **No available tokens**: Check token validity and daily limits
2. **Proxy connection errors**: Verify proxy configuration and connectivity
3. **Authentication failures**: Ensure tokens are valid and active
4. **Rate limiting**: Tokens may have hit rate limits, rotation will handle this

### Debug Mode

Enable debug logging to see detailed information:

```bash
python forge_rotating_client.py --message "test" --log-level DEBUG
```

This will show:
- Token rotation decisions
- Proxy usage
- Request/response details
- Error details

## License

This script is provided as-is for educational and research purposes.
