#!/usr/bin/env python3
"""
Example usage of the Forge Rotating Token Client

This script demonstrates various ways to use the ForgeClient for accessing
Forge Dev LLMs with token rotation and proxy support.
"""

import logging
from forge_rotating_client import ForgeClient, setup_logging


def example_simple_chat():
    """Example of simple chat interface."""
    print("=== Simple Chat Example ===")
    
    client = ForgeClient()
    
    # Simple question
    response = client.simple_chat("What is the capital of France?")
    print(f"Q: What is the capital of France?")
    print(f"A: {response}\n")


def example_streaming_chat():
    """Example of streaming chat responses."""
    print("=== Streaming Chat Example ===")
    
    client = ForgeClient()
    
    messages = [
        {"role": "user", "content": "Write a short poem about programming"}
    ]
    
    print("Q: Write a short poem about programming")
    print("A: ", end="", flush=True)
    
    for chunk in client.chat_completion(messages, stream=True):
        if 'choices' in chunk and chunk['choices']:
            delta = chunk['choices'][0].get('delta', {})
            if 'content' in delta:
                print(delta['content'], end='', flush=True)
    
    print("\n")


def example_conversation():
    """Example of multi-turn conversation."""
    print("=== Multi-turn Conversation Example ===")
    
    client = ForgeClient()
    
    # Build conversation history
    messages = [
        {"role": "user", "content": "Hello! Can you help me with Python?"},
    ]
    
    # First response
    print("User: Hello! Can you help me with Python?")
    response_text = ""
    for chunk in client.chat_completion(messages, stream=True):
        if 'choices' in chunk and chunk['choices']:
            delta = chunk['choices'][0].get('delta', {})
            if 'content' in delta:
                response_text += delta['content']
    
    print(f"Assistant: {response_text}")
    
    # Add assistant response to conversation
    messages.append({"role": "assistant", "content": response_text})
    
    # Follow-up question
    messages.append({"role": "user", "content": "What's the difference between lists and tuples?"})
    
    print("\nUser: What's the difference between lists and tuples?")
    print("Assistant: ", end="", flush=True)
    
    for chunk in client.chat_completion(messages, stream=True):
        if 'choices' in chunk and chunk['choices']:
            delta = chunk['choices'][0].get('delta', {})
            if 'content' in delta:
                print(delta['content'], end='', flush=True)
    
    print("\n")


def example_different_models():
    """Example of using different models."""
    print("=== Different Models Example ===")
    
    client = ForgeClient()
    
    question = "Explain quantum computing in one sentence."
    
    models = [
        "anthropic/claude-sonnet-4",
        "openai/gpt-4",
        "meta-llama/llama-3.1-405b-instruct"
    ]
    
    for model in models:
        try:
            print(f"\nModel: {model}")
            response = client.simple_chat(question, model=model)
            print(f"Response: {response}")
        except Exception as e:
            print(f"Error with {model}: {e}")


def example_custom_parameters():
    """Example of using custom parameters."""
    print("=== Custom Parameters Example ===")
    
    client = ForgeClient()
    
    messages = [
        {"role": "user", "content": "Generate a creative story about a robot."}
    ]
    
    print("High creativity (temperature=1.0):")
    for chunk in client.chat_completion(
        messages, 
        temperature=1.0, 
        max_tokens=200,
        stream=True
    ):
        if 'choices' in chunk and chunk['choices']:
            delta = chunk['choices'][0].get('delta', {})
            if 'content' in delta:
                print(delta['content'], end='', flush=True)
    
    print("\n\nLow creativity (temperature=0.1):")
    for chunk in client.chat_completion(
        messages, 
        temperature=0.1, 
        max_tokens=200,
        stream=True
    ):
        if 'choices' in chunk and chunk['choices']:
            delta = chunk['choices'][0].get('delta', {})
            if 'content' in delta:
                print(delta['content'], end='', flush=True)
    
    print("\n")


def example_error_handling():
    """Example of error handling and token rotation."""
    print("=== Error Handling Example ===")
    
    client = ForgeClient()
    
    try:
        # This should work with token rotation
        response = client.simple_chat("Hello, how are you?")
        print(f"Success: {response[:100]}...")
        
    except Exception as e:
        print(f"Error occurred: {e}")
        
    # Check token status
    print(f"\nAvailable tokens: {len([t for t in client.token_manager.tokens if t.get('is_active', True)])}")
    print(f"Available proxies: {len([p for p in client.proxy_manager.proxies if p.get('is_active', True)])}")


def main():
    """Run all examples."""
    # Setup logging
    setup_logging("INFO")
    
    print("Forge Rotating Token Client - Examples")
    print("=" * 50)
    
    try:
        example_simple_chat()
        example_streaming_chat()
        example_conversation()
        example_different_models()
        example_custom_parameters()
        example_error_handling()
        
    except Exception as e:
        logging.error(f"Example failed: {e}")
        print(f"Error running examples: {e}")
    
    print("\nAll examples completed!")


if __name__ == "__main__":
    main()
