#!/usr/bin/env python3
"""
Test script for the Forge Rotating Token Client

This script tests various components of the ForgeClient to ensure
everything is working correctly with the existing configuration.
"""

import json
import logging
import sys
from forge_rotating_client import ForgeClient, TokenManager, ProxyManager, setup_logging


def test_config_loading():
    """Test loading configuration from proxy_config.json."""
    print("Testing configuration loading...")
    
    config_path = "/home/<USER>/workspace/forge/proxy_config.json"
    
    try:
        # Test TokenManager
        token_manager = TokenManager(config_path)
        print(f"✓ Loaded {len(token_manager.tokens)} tokens")
        
        for i, token in enumerate(token_manager.tokens):
            print(f"  Token {i+1}: ...{token['token'][-8:]} (active: {token.get('is_active', True)})")
        
        # Test ProxyManager
        proxy_manager = ProxyManager(config_path)
        print(f"✓ Loaded {len(proxy_manager.proxies)} proxies")
        
        for i, proxy in enumerate(proxy_manager.proxies):
            print(f"  Proxy {i+1}: {proxy['url']} (active: {proxy.get('is_active', True)})")
        
        return True
        
    except Exception as e:
        print(f"✗ Configuration loading failed: {e}")
        return False


def test_token_rotation():
    """Test token rotation functionality."""
    print("\nTesting token rotation...")
    
    try:
        config_path = "/home/<USER>/workspace/forge/proxy_config.json"
        token_manager = TokenManager(config_path)
        
        # Test round-robin rotation
        print("Round-robin rotation:")
        for i in range(5):
            token = token_manager.get_next_token("round_robin")
            if token:
                print(f"  Attempt {i+1}: ...{token[-8:]}")
            else:
                print(f"  Attempt {i+1}: No token available")
        
        # Test random rotation
        print("Random rotation:")
        for i in range(3):
            token = token_manager.get_next_token("random")
            if token:
                print(f"  Attempt {i+1}: ...{token[-8:]}")
            else:
                print(f"  Attempt {i+1}: No token available")
        
        print("✓ Token rotation working")
        return True
        
    except Exception as e:
        print(f"✗ Token rotation failed: {e}")
        return False


def test_proxy_rotation():
    """Test proxy rotation functionality."""
    print("\nTesting proxy rotation...")
    
    try:
        config_path = "/home/<USER>/workspace/forge/proxy_config.json"
        proxy_manager = ProxyManager(config_path)
        
        for i in range(3):
            proxy = proxy_manager.get_next_proxy()
            if proxy:
                print(f"  Proxy {i+1}: {list(proxy.values())[0] if proxy else 'None'}")
            else:
                print(f"  Proxy {i+1}: No proxy available")
        
        print("✓ Proxy rotation working")
        return True
        
    except Exception as e:
        print(f"✗ Proxy rotation failed: {e}")
        return False


def test_client_initialization():
    """Test ForgeClient initialization."""
    print("\nTesting client initialization...")
    
    try:
        client = ForgeClient()
        print("✓ ForgeClient initialized successfully")
        
        # Check if telemetry is disabled
        import os
        telemetry_vars = ['FORGE_TRACKER', 'DO_NOT_TRACK', 'TELEMETRY_DISABLED']
        for var in telemetry_vars:
            value = os.environ.get(var)
            print(f"  {var}: {value}")
        
        return True
        
    except Exception as e:
        print(f"✗ Client initialization failed: {e}")
        return False


def test_api_request():
    """Test making an actual API request (optional - requires valid tokens)."""
    print("\nTesting API request...")
    
    try:
        client = ForgeClient()
        
        # Simple test message
        test_message = "Hello! Please respond with just 'Hello back!' and nothing else."
        
        print(f"Sending test message: {test_message}")
        response = client.simple_chat(test_message)
        
        print(f"✓ API request successful")
        print(f"Response: {response[:100]}{'...' if len(response) > 100 else ''}")
        
        return True
        
    except Exception as e:
        print(f"✗ API request failed: {e}")
        print("This might be expected if tokens are invalid or rate limited")
        return False


def test_streaming_request():
    """Test streaming API request (optional - requires valid tokens)."""
    print("\nTesting streaming API request...")
    
    try:
        client = ForgeClient()
        
        messages = [{"role": "user", "content": "Count from 1 to 5, one number per line."}]
        
        print("Streaming response:")
        response_text = ""
        for chunk in client.chat_completion(messages, stream=True, max_tokens=50):
            if 'choices' in chunk and chunk['choices']:
                delta = chunk['choices'][0].get('delta', {})
                if 'content' in delta:
                    content = delta['content']
                    response_text += content
                    print(content, end='', flush=True)
        
        print(f"\n✓ Streaming request successful")
        print(f"Complete response: {response_text.strip()}")
        
        return True
        
    except Exception as e:
        print(f"✗ Streaming request failed: {e}")
        print("This might be expected if tokens are invalid or rate limited")
        return False


def test_usage_tracking():
    """Test usage tracking functionality."""
    print("\nTesting usage tracking...")
    
    try:
        config_path = "/home/<USER>/workspace/forge/proxy_config.json"
        token_manager = TokenManager(config_path)
        
        # Get a token and mark it as used
        token = token_manager.get_next_token()
        if token:
            print(f"Got token: ...{token[-8:]}")
            
            # Check usage before
            for t in token_manager.tokens:
                if t['token'] == token:
                    usage_before = t.get('used_today', 0)
                    print(f"Usage before: {usage_before}")
                    break
            
            # Mark as used
            token_manager.mark_token_used(token)
            
            # Check usage after
            for t in token_manager.tokens:
                if t['token'] == token:
                    usage_after = t.get('used_today', 0)
                    print(f"Usage after: {usage_after}")
                    break
            
            if usage_after > usage_before:
                print("✓ Usage tracking working")
                return True
            else:
                print("✗ Usage not incremented")
                return False
        else:
            print("✗ No token available for testing")
            return False
        
    except Exception as e:
        print(f"✗ Usage tracking failed: {e}")
        return False


def main():
    """Run all tests."""
    setup_logging("INFO")
    
    print("Forge Rotating Token Client - Test Suite")
    print("=" * 50)
    
    tests = [
        test_config_loading,
        test_token_rotation,
        test_proxy_rotation,
        test_client_initialization,
        test_usage_tracking,
    ]
    
    # Optional tests that require valid tokens
    optional_tests = [
        test_api_request,
        test_streaming_request,
    ]
    
    passed = 0
    total = len(tests)
    
    # Run core tests
    for test in tests:
        if test():
            passed += 1
    
    print(f"\nCore Tests: {passed}/{total} passed")
    
    # Ask user if they want to run API tests
    try:
        run_api_tests = input("\nRun API tests? (requires valid tokens) [y/N]: ").lower().startswith('y')
    except (EOFError, KeyboardInterrupt):
        run_api_tests = False
    
    if run_api_tests:
        api_passed = 0
        for test in optional_tests:
            if test():
                api_passed += 1
        
        print(f"API Tests: {api_passed}/{len(optional_tests)} passed")
        total += len(optional_tests)
        passed += api_passed
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed!")
        sys.exit(0)
    else:
        print("⚠️  Some tests failed")
        sys.exit(1)


if __name__ == "__main__":
    main()
