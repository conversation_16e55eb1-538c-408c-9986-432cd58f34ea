{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Workflow", "description": "Configuration for a workflow that contains all settings required to initialize a workflow.", "type": "object", "properties": {"agents": {"description": "Agents that are part of this workflow", "type": "array", "items": {"$ref": "#/definitions/Agent"}}, "commands": {"description": "Commands that can be used to interact with the workflow", "type": "array", "items": {"$ref": "#/definitions/Command"}}, "compact": {"description": "Configuration for automatic context compaction for all agents If specified, this will be applied to all agents in the workflow If not specified, each agent's individual setting will be used", "anyOf": [{"$ref": "#/definitions/Compact"}, {"type": "null"}]}, "custom_rules": {"description": "A set of custom rules that all agents should follow These rules will be applied in addition to each agent's individual rules", "type": ["string", "null"]}, "max_requests_per_turn": {"description": "Maximum number of requests that can be made in a single turn", "type": ["integer", "null"], "format": "uint", "minimum": 0.0}, "max_tokens": {"description": "Maximum number of tokens the model can generate for all agents\n\nControls the maximum length of the model's response. - Lower values (e.g., 100) limit response length for concise outputs - Higher values (e.g., 4000) allow for longer, more detailed responses - Valid range is 1 to 100,000 - If not specified, each agent's individual setting or the model provider's default will be used", "anyOf": [{"$ref": "#/definitions/MaxTokens"}, {"type": "null"}]}, "max_tool_failure_per_turn": {"description": "Maximum number of times a tool can fail before the orchestrator forces the completion.", "type": ["integer", "null"], "format": "uint", "minimum": 0.0}, "max_walker_depth": {"description": "Maximum depth to which the file walker should traverse for all agents If not provided, each agent's individual setting will be used", "type": ["integer", "null"], "format": "uint", "minimum": 0.0}, "model": {"description": "Default model ID to use for agents in this workflow", "type": ["string", "null"]}, "temperature": {"description": "Temperature used for all agents\n\nTemperature controls the randomness in the model's output. - Lower values (e.g., 0.1) make responses more focused, deterministic, and coherent - Higher values (e.g., 0.8) make responses more creative, diverse, and exploratory - Valid range is 0.0 to 2.0 - If not specified, each agent's individual setting or the model provider's default will be used", "anyOf": [{"$ref": "#/definitions/Temperature"}, {"type": "null"}]}, "templates": {"description": "Path pattern for custom template files (supports glob patterns)", "type": ["string", "null"]}, "tool_supported": {"description": "Flag to enable/disable tool support for all agents in this workflow. If not specified, each agent's individual setting will be used. Default is false (tools disabled) when not specified.", "type": ["boolean", "null"]}, "top_k": {"description": "Top-k used for all agents\n\nControls the number of highest probability vocabulary tokens to keep. - Lower values (e.g., 10) make responses more focused - Higher values (e.g., 100) make responses more diverse - Valid range is 1 to 1000 - If not specified, each agent's individual setting or the model provider's default will be used", "anyOf": [{"$ref": "#/definitions/TopK"}, {"type": "null"}]}, "top_p": {"description": "Top-p (nucleus sampling) used for all agents\n\nControls the diversity of the model's output by considering only the most probable tokens up to a cumulative probability threshold. - Lower values (e.g., 0.1) make responses more focused - Higher values (e.g., 0.9) make responses more diverse - Valid range is 0.0 to 1.0 - If not specified, each agent's individual setting or the model provider's default will be used", "anyOf": [{"$ref": "#/definitions/TopP"}, {"type": "null"}]}, "updates": {"description": "configurations that can be used to update forge", "anyOf": [{"$ref": "#/definitions/Update"}, {"type": "null"}]}, "variables": {"description": "Variables that can be used in templates", "type": "object", "additionalProperties": true}}, "definitions": {"Agent": {"type": "object", "required": ["id"], "properties": {"compact": {"description": "Configuration for automatic context compaction", "anyOf": [{"$ref": "#/definitions/Compact"}, {"type": "null"}]}, "custom_rules": {"description": "A set of custom rules that the agent should follow", "type": ["string", "null"]}, "description": {"type": ["string", "null"]}, "id": {"type": "string"}, "max_tokens": {"description": "Maximum number of tokens the model can generate\n\nControls the maximum length of the model's response. - Lower values (e.g., 100) limit response length for concise outputs - Higher values (e.g., 4000) allow for longer, more detailed responses - Valid range is 1 to 100,000 - If not specified, the model provider's default will be used", "anyOf": [{"$ref": "#/definitions/MaxTokens"}, {"type": "null"}]}, "max_turns": {"description": "Maximum number of turns the agent can take", "type": ["integer", "null"], "format": "uint64", "minimum": 0.0}, "max_walker_depth": {"description": "Maximum depth to which the file walker should traverse for this agent If not provided, the maximum possible depth will be used", "type": ["integer", "null"], "format": "uint", "minimum": 0.0}, "model": {"type": ["string", "null"]}, "reasoning": {"description": "Reasoning configuration for the agent. Controls the reasoning capabilities of the agent", "anyOf": [{"$ref": "#/definitions/ReasoningConfig"}, {"type": "null"}]}, "subscribe": {"description": "Used to specify the events the agent is interested in", "type": ["array", "null"], "items": {"type": "string"}}, "system_prompt": {"anyOf": [{"$ref": "#/definitions/String"}, {"type": "null"}]}, "temperature": {"description": "Temperature used for agent\n\nTemperature controls the randomness in the model's output. - Lower values (e.g., 0.1) make responses more focused, deterministic, and coherent - Higher values (e.g., 0.8) make responses more creative, diverse, and exploratory - Valid range is 0.0 to 2.0 - If not specified, the model provider's default temperature will be used", "anyOf": [{"$ref": "#/definitions/Temperature"}, {"type": "null"}]}, "title": {"description": "Human-readable title for the agent", "type": ["string", "null"]}, "tool_supported": {"description": "Flag to enable/disable tool support for this agent.", "type": ["boolean", "null"]}, "tools": {"description": "Tools that the agent can use", "type": ["array", "null"], "items": {"type": "string"}}, "top_k": {"description": "Top-k used for agent\n\nControls the number of highest probability vocabulary tokens to keep. - Lower values (e.g., 10) make responses more focused - Higher values (e.g., 100) make responses more diverse - Valid range is 1 to 1000 - If not specified, the model provider's default will be used", "anyOf": [{"$ref": "#/definitions/TopK"}, {"type": "null"}]}, "top_p": {"description": "Top-p (nucleus sampling) used for agent\n\nControls the diversity of the model's output by considering only the most probable tokens up to a cumulative probability threshold. - Lower values (e.g., 0.1) make responses more focused - Higher values (e.g., 0.9) make responses more diverse - Valid range is 0.0 to 1.0 - If not specified, the model provider's default will be used", "anyOf": [{"$ref": "#/definitions/TopP"}, {"type": "null"}]}, "user_prompt": {"anyOf": [{"$ref": "#/definitions/String"}, {"type": "null"}]}}}, "Command": {"type": "object", "required": ["description", "name"], "properties": {"description": {"type": "string"}, "name": {"type": "string"}, "prompt": {"type": ["string", "null"]}}}, "Compact": {"description": "Configuration for automatic context compaction", "type": "object", "required": ["eviction_window", "model", "retention_window"], "properties": {"eviction_window": {"description": "Maximum percentage of the context that can be summarized during compaction. Valid values are between 0.0 and 1.0, where 0.0 means no compaction and 1.0 allows summarizing all messages. Works alongside retention_window - the more conservative limit (fewer messages to compact) takes precedence.", "type": "number", "format": "double"}, "max_tokens": {"description": "Maximum number of tokens to keep after compaction", "type": ["integer", "null"], "format": "uint", "minimum": 0.0}, "message_threshold": {"description": "Maximum number of messages before triggering compaction", "type": ["integer", "null"], "format": "uint", "minimum": 0.0}, "model": {"description": "Model ID to use for compaction, useful when compacting with a cheaper/faster model", "type": "string"}, "on_turn_end": {"description": "Whether to trigger compaction when the last message is from a user", "type": ["boolean", "null"]}, "prompt": {"description": "Optional custom prompt template to use during compaction", "type": ["string", "null"]}, "retention_window": {"description": "Number of most recent messages to preserve during compaction. These messages won't be considered for summarization. Works alongside eviction_window - the more conservative limit (fewer messages to compact) takes precedence.", "type": "integer", "format": "uint", "minimum": 0.0}, "summary_tag": {"description": "Optional tag name to extract content from when summarizing (e.g., \"summary\")", "type": ["string", "null"]}, "token_threshold": {"description": "Maximum number of tokens before triggering compaction", "type": ["integer", "null"], "format": "uint", "minimum": 0.0}, "turn_threshold": {"description": "Maximum number of conversation turns before triggering compaction", "type": ["integer", "null"], "format": "uint", "minimum": 0.0}}}, "Effort": {"type": "string", "enum": ["high", "medium", "low"]}, "MaxTokens": {"description": "A newtype for max_tokens values with built-in validation\n\nMax tokens controls the maximum number of tokens the model can generate: - Lower values (e.g., 100) limit response length for concise outputs - Higher values (e.g., 4000) allow for longer, more detailed responses - Valid range is 1 to 100,000 (reasonable upper bound for most models) - If not specified, the model provider's default will be used", "type": "integer", "format": "uint32", "minimum": 0.0}, "ReasoningConfig": {"type": "object", "properties": {"effort": {"description": "Controls the effort level of the agent's reasoning supported by openrouter and forge provider", "anyOf": [{"$ref": "#/definitions/Effort"}, {"type": "null"}]}, "enabled": {"description": "Enables reasoning at the “medium” effort level with no exclusions. supported by openrouter, anthropic and forge provider", "type": ["boolean", "null"]}, "exclude": {"description": "Model thinks deeply, but the reasoning is hidden from you. supported by openrouter and forge provider", "type": ["boolean", "null"]}, "max_tokens": {"description": "Controls how many tokens the model can spend thinking. supported by openrouter, anthropic and forge provider should be greater then 1024 but less than overall max_tokens", "type": ["integer", "null"], "format": "uint", "minimum": 0.0}}}, "String": {"type": "string"}, "Temperature": {"description": "A newtype for temperature values with built-in validation\n\nTemperature controls the randomness in the model's output: - Lower values (e.g., 0.1) make responses more focused, deterministic, and coherent - Higher values (e.g., 0.8) make responses more creative, diverse, and exploratory - Valid range is 0.0 to 2.0", "type": "number", "format": "float"}, "TopK": {"description": "A newtype for top_k values with built-in validation\n\nTop-k controls the number of highest probability vocabulary tokens to keep: - Lower values (e.g., 10) make responses more focused by considering only the top K most likely tokens - Higher values (e.g., 100) make responses more diverse by considering more token options - Valid range is 1 to 1000 (inclusive)", "type": "integer", "format": "uint32", "minimum": 0.0}, "TopP": {"description": "A newtype for top_p values with built-in validation\n\nTop-p (nucleus sampling) controls the diversity of the model's output: - Lower values (e.g., 0.1) make responses more focused by considering only the most probable tokens - Higher values (e.g., 0.9) make responses more diverse by considering a broader range of tokens - Valid range is 0.0 to 1.0", "type": "number", "format": "float"}, "Update": {"type": "object", "properties": {"auto_update": {"type": ["boolean", "null"]}, "frequency": {"anyOf": [{"$ref": "#/definitions/UpdateFrequency"}, {"type": "null"}]}}}, "UpdateFrequency": {"type": "string", "enum": ["daily", "weekly", "always"]}}}